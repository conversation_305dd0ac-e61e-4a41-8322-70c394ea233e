apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth
  namespace: vela-system
spec:
  replicas: 1
  selector:
    matchLabels:
      name: auth
  template:
    metadata:
      labels:
        name: auth
    spec:
      imagePullSecrets:
       - name: ksyunregistrykey
      containers:
        - name: auth
          image: harbor.sdns.kscbigdata.cloud:11180/kbdp/kcde-auth-server:0.4-arm
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 7070
              protocol: TCP
          volumeMounts:
            - mountPath: /data/apps/kcde-auth-server/config/
              name: host-time
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 1000m
              memory: 1Gi
          env:
            - name: auth_server_memory
              value: 1
      volumes:
        - name: host-time
          configMap:
            name: auth-config