package com.ksyun.auth.server.handler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ksyun.auth.client.cache.CacheUtil;
import com.ksyun.auth.server.constant.SecurityConstants;
import com.ksyun.auth.server.utils.CookieUtils;
import com.ksyun.auth.server.utils.NonceIdUtils;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jodd.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.RedirectStrategy;
import org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @description 校验未通过时, 重定向志登录处理
 */
@Slf4j
public class LoginTargetAuthenticationEntryPoint extends LoginUrlAuthenticationEntryPoint {

    private final RedirectStrategy redirectStrategy = new DefaultRedirectStrategy();

    private String serverUrl;
    private String ngServerUrl;

    public LoginTargetAuthenticationEntryPoint(String loginFormUrl, String authServerUrl, String ngServerUrl) {
        super(loginFormUrl);
        this.serverUrl = authServerUrl;
        this.ngServerUrl = ngServerUrl;
    }

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException, ServletException {
        // 获取登录表单的地址
        String loginForm = determineUrlToUseForThisRequest(request, response, authException);
        //String nonceId = !ObjectUtils.isEmpty(request.getSession(Boolean.FALSE)) ? request.getSession(Boolean.FALSE).getId() : UUID.randomUUID().toString();
        String nonceId = NonceIdUtils.getRandomNonce(request);
        CookieUtils.setNonceIdCookie(request, response, "1", nonceId);
        //判断是否是客户端请求.如果是客户端请求需要手动重定向.
        if (!StringUtils.isEmpty(request.getParameter("client_id"))) {
            String replaceUrl = request.getRequestURL().toString();
            log.debug("replaceUrl = [{}]", replaceUrl);
            String requestUrl = replaceUrl.replace(ngServerUrl, serverUrl);
            log.debug("requestUrl = [{}]", requestUrl);
            if (!ObjectUtils.isEmpty(request.getQueryString())) {
                requestUrl = requestUrl + "?" + request.getQueryString();
            }
            String targetParameter = URLEncoder.encode(requestUrl.toString(), StandardCharsets.UTF_8);
            CacheUtil.set(SecurityConstants.NONCE_TARGET_URL_PREFIX_KEY + nonceId, requestUrl.toString(), SecurityConstants.DEFAULT_TARGATURL_EXPIRATION_TIME);
            log.debug("客户端请求重定的targetParameter [{}]", requestUrl.toString());
            String loginPageUrl = loginForm + "?target=" + targetParameter + "&" + SecurityConstants.NONCE_HEADER_NAME + "=" + nonceId;
            log.debug("客户端请求重定向地址 [{}]", loginPageUrl);
            this.redirectStrategy.sendRedirect(request, response, loginPageUrl);
        } else {
            String refererUrl = request.getHeader("referer");
            if(StringUtils.isNotEmpty(refererUrl) && refererUrl.contains("/dlstudio")){
                CacheUtil.set(SecurityConstants.NONCE_TARGET_URL_PREFIX_KEY + nonceId, refererUrl, SecurityConstants.DEFAULT_TARGATURL_EXPIRATION_TIME);
                log.debug("dls targetParameter [{}]",refererUrl);
            }
            //服务端请求.直接返回401,让前端进行重定向
            String loginPageUrl = loginForm + "?" + SecurityConstants.NONCE_HEADER_NAME + "=" + nonceId;
            response.setStatus(HttpStatus.HTTP_UNAUTHORIZED);
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json; charset=utf-8");
            Map<String, Object> data = new HashMap<String, Object>() {{
                put("status", 401);
                put("message", "未授权");
                put("loginPageUrl", loginPageUrl);
            }};
            log.debug("服务端请求重定向地址 = [{}] ", loginPageUrl);
            response.getWriter().write(JSON.toJSONString(data));
        }
    }
}
