package com.ksyun.auth.server.utils;

import com.ksyun.auth.utils.PropConfig;
import com.ksyun.common.utils.DomainUtils;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.util.Objects;
import java.util.UUID;

import static com.ksyun.auth.server.constant.SecurityConstants.NONCE_HEADER_NAME;
@Slf4j
public class NonceIdUtils {

    /**
     *  1.从cookie中获取nonceid,
     *  2.若不存在，则从session中获取，
     *  3.若session也不存在，则使用uuid生成
     *
     * @param request 当前请求
     * @return 随机字符串(sessionId)
     */
    public static String getNonce(HttpServletRequest request) {
        String cookieNonceId = getCookieNonceId(request);
        if (!ObjectUtils.isEmpty(cookieNonceId)){
            log.debug("使用cookie中存储的值作为nonceid");
            return cookieNonceId;
        }else if (!ObjectUtils.isEmpty(request.getSession(Boolean.FALSE))){
            HttpSession session = request.getSession(Boolean.FALSE);
            log.debug("使用session中的值为作为nonceid");
            return session.getId();
        }else {
            log.debug("使用新uuid 作为nonceid");
            return UUID.randomUUID().toString();
        }
    }

    public static String getCookieNonceId(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        String domain = DomainUtils.getDomain(request, PropConfig.AUTH_SERVER_DOMAIN, "1");
        if (Objects.nonNull(cookies) && cookies.length > 0 && Objects.nonNull(domain) ) {
            for (Cookie cookie : cookies) {
                if( cookie.getName().equalsIgnoreCase(NONCE_HEADER_NAME)){
                    log.debug("nonceId ==> cookieValue={}" , cookie.getValue());
                    return cookie.getValue();
                }else{
                    log.debug("cookieName={},cookieDomain={},domain={}",cookie.getName(),cookie.getDomain(),domain);
                }
            }
        }
        return null;
    }
    public static String getRandomNonce(HttpServletRequest request) {
        log.debug("使用新uuid 作为nonceid");
        return UUID.randomUUID().toString();
    }
}
