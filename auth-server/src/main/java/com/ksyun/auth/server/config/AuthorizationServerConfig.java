package com.ksyun.auth.server.config;

import com.ksyun.auth.server.handler.LoginTargetAuthenticationEntryPoint;
import com.ksyun.auth.server.security.handler.DomainRequestMatcher;
import com.ksyun.auth.server.security.handler.OidcCustomProviderConfigurationEndpointFilter;
import com.ksyun.auth.server.support.MemorySecurityContextRepository;
import com.ksyun.auth.server.utils.ObjectPostProcessorUtils;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.source.ImmutableJWKSet;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.proc.SecurityContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configuration.OAuth2AuthorizationServerConfiguration;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configurers.OAuth2AuthorizationServerConfigurer;
import org.springframework.security.oauth2.server.authorization.oidc.web.OidcProviderConfigurationEndpointFilter;
import org.springframework.security.oauth2.server.authorization.settings.AuthorizationServerSettings;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.util.matcher.MediaTypeRequestMatcher;
import org.springframework.security.web.util.matcher.OrRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.UUID;

import static org.springframework.security.config.Customizer.withDefaults;

/**
 * 认证服务器配置
 */
@Configuration
@EnableWebSecurity
@Slf4j
@RequiredArgsConstructor
@EnableConfigurationProperties(Oauth2ServerProps.class)
public class AuthorizationServerConfig {

    private final Oauth2ServerProps oauth2ServerProps;

    private final MemorySecurityContextRepository memorySecurityContextRepository;

    private final RsaKeyLoader rsaKeyLoader;

    /**
     * 配置 Provider元数据
     */
    @Bean
    public AuthorizationServerSettings providerSettings() {
        return AuthorizationServerSettings.builder()
                .issuer(this.oauth2ServerProps.getIssuer())
                .authorizationEndpoint(this.oauth2ServerProps.getAuthorizationEndpoint())
                .tokenEndpoint(this.oauth2ServerProps.getTokenEndpoint())
                .jwkSetEndpoint(this.oauth2ServerProps.getJwkSetEndpoint())
                .oidcUserInfoEndpoint(this.oauth2ServerProps.getOidcUserInfoEndpoint())
                .tokenIntrospectionEndpoint(this.oauth2ServerProps.getTokenIntrospectionEndpoint())
                .tokenRevocationEndpoint(this.oauth2ServerProps.getTokenRevocationEndpoint())
                .oidcLogoutEndpoint(this.oauth2ServerProps.getEndSessionEndpoint())
                .build();
    }

    /**
     * 认证服务器核心配置类,最优先启用配置
     * 设置所有的API 受Security框架保护，并配置表单登录和Oauth2Login
     * @param http
     * @return
     * @throws Exception
     */
    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE)
    public SecurityFilterChain authorizationServerSecurityFilterChain(HttpSecurity http)
            throws Exception {

        // 开启认证服务默认配置、主要包括各种认证端点
        OAuth2AuthorizationServerConfiguration.applyDefaultSecurity(http);

        // 启用OIDC支持，增加了OIDC相关的三个端点
        OAuth2AuthorizationServerConfigurer authorizationServerConfigurer = http.getConfigurer(OAuth2AuthorizationServerConfigurer.class);
        // 获取OAuth2协议端点 用于进行一些个性化配置
        RequestMatcher endpointsMatcher = authorizationServerConfigurer.getEndpointsMatcher();

        // OIDC配置
        authorizationServerConfigurer
                // oidc 配置 开启客户端动态注册功能
                .oidc(oidc -> oidc.clientRegistrationEndpoint(Customizer.withDefaults()))
                .withObjectPostProcessor(ObjectPostProcessorUtils.objectPostReturnNewObj(
                        OncePerRequestFilter.class,
                        OidcProviderConfigurationEndpointFilter.class,
                        new OidcCustomProviderConfigurationEndpointFilter(this.providerSettings(), this.oauth2ServerProps)))
                // 客户端认证配置
                .clientAuthentication(withDefaults())
        ;

//        RequestMatcher domainRequestMatcher = new DomainRequestMatcher(this.oauth2ServerProps.getAuthorizationEndpoint());
//        RequestMatcher fixRequestMatcher = new OrRequestMatcher(endpointsMatcher, domainRequestMatcher);

        //仅拦截OAuth2 Authorization Server的相关endpoint
        http
//                .securityMatcher(fixRequestMatcher)
                // 所有Oauth协议端点不受csrf保护
                .csrf(csrf -> csrf.ignoringRequestMatchers(endpointsMatcher))
                .oauth2ResourceServer(oauth2ResourceServer -> oauth2ResourceServer.jwt(Customizer.withDefaults()))
        ;
        // 指定认证信息存储在内存中.后续也从内存中读取认证信息.key为nonceId
        http.
                securityContext(context -> context.securityContextRepository(memorySecurityContextRepository));
        http
                .formLogin(form ->
                        form
                                .loginPage(this.oauth2ServerProps.getLoginPageUrl())
                                .loginProcessingUrl(this.oauth2ServerProps.getLoginProcessingUrl())
                )
                //发现未登录时。重定向登录地址
                .exceptionHandling((exceptions) -> exceptions
                        .defaultAuthenticationEntryPointFor(
                                new LoginTargetAuthenticationEntryPoint(this.oauth2ServerProps.getLoginPageUrl(),this.oauth2ServerProps.getAuthServerUrl(),this.oauth2ServerProps.getNgServerUrl()),
                                new MediaTypeRequestMatcher(MediaType.TEXT_HTML)
                        ))
        ;
        return http.build();
    }



    /**
     * 用户查询服务，用于Provider组件验证身份，暂时写死
     * 这里的账密没有实际的使用.只是初始化需要.
     * @return
     */
    @Bean
    public UserDetailsService userDetailsService() {
        UserDetails userDetails = User.withDefaultPasswordEncoder()
                .username("admin")
                .password("123456")
                .build();

        return new InMemoryUserDetailsManager(userDetails);
    }
//    @Bean
//    public RegisteredClientRepository registeredClientRepository(JdbcTemplate jdbcTemplate) {
//        //基于db存储客户端
//        JdbcRegisteredClientRepository registeredClientRepository = new JdbcRegisteredClientRepository(jdbcTemplate);
//        return registeredClientRepository;
//    }

    @Bean
    public JWKSource<SecurityContext> jwkSource() throws Exception {

        KeyPair keyPair = rsaKeyLoader.loadRsaKeyPair();
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        RSAKey rsaKey = new RSAKey.Builder(publicKey)
                .privateKey(privateKey)
                .keyID(UUID.randomUUID().toString())
                .build();
        JWKSet jwkSet = new JWKSet(rsaKey);
        return new ImmutableJWKSet<>(jwkSet);
    }

//    private static KeyPair generateRsaKey() {
//        KeyPair keyPair;
//        try {
//            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
//            keyPairGenerator.initialize(2048);
//            keyPair = keyPairGenerator.generateKeyPair();
//        }
//        catch (Exception ex) {
//            throw new IllegalStateException(ex);
//        }
//        return keyPair;
//    }

    @Bean
    public JwtDecoder jwtDecoder(JWKSource<SecurityContext> jwkSource) {
        return OAuth2AuthorizationServerConfiguration.jwtDecoder(jwkSource);
    }

    /**
     *  客户端秘钥加密方式
     * @return
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
