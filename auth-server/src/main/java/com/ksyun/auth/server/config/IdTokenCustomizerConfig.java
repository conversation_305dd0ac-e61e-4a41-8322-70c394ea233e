/*
 * Copyright 2020-2022 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ksyun.auth.server.config;

import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.service.PrivilegeService;
import com.ksyun.auth.service.UserService;
import com.ksyun.auth.vo.UserVo;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.core.oidc.endpoint.OidcParameterNames;
import org.springframework.security.oauth2.server.authorization.token.JwtEncodingContext;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenCustomizer;

import java.security.Key;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Configuration
public class IdTokenCustomizerConfig {


	// @formatter:off
	@Bean // <1>
	public OAuth2TokenCustomizer<JwtEncodingContext> tokenCustomizer(UserService userService, PrivilegeService privilegeService) {
		return (context) -> {
			if (OidcParameterNames.ID_TOKEN.equals(context.getTokenType().getValue())) {

				Authentication authentication = context.getPrincipal();
				AuthUser authUser = (AuthUser)authentication;

				UserVo userVo = userService.getUserById(authUser.getId()).get();

				List<String> privilegeCode = privilegeService.getPrivilegeByUser(null, authUser, null);
				context.getClaims().claim("account_id", "");
				context.getClaims().claim("user_id", String.valueOf(userVo.getId()));
				context.getClaims().claim("user_name", userVo.getName());
				context.getClaims().claim("user_email", userVo.getEmail());
				context.getClaims().claim("user_full_name", userVo.getAlias());
				context.getClaims().claim("user_department", ""); // 这里假设部门信息固定，实际应从用户信息中获取
				context.getClaims().claim("permissions", privilegeCode);
				context.getClaims().claim("data_version", "-1");
				context.getClaims().claim("key_version", "-1");
			}
		};
	}
    // @formatter:on

}
