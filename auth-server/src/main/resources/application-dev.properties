# æ¬å°è¿æ¥æ°æ®æº
spring.datasource.url=************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=Kingsoft_com123!
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

mybatis-plus.mapper-locations=classpath*:mapper/*.xml
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# çæ Cookie å¹²é¢å¼
cookie.salt=secret.dev

# è®¤è¯ä¸­å¿
auth.server.url=http://127.0.0.1:8090/authserver

spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-request-size=10MB
spring.servlet.multipart.max-file-size=10MB

auth-server.map.bigdata.url=http://127.0.0.1:8090/authserver
auth-server.map.bigdata.cookieNames=sso-token
auth-server.map.bigdata.cacheExpire=30
auth-server.map.bigdata.loginCacheTime=60
auth-server.map.bigdata.domain=127.0.0.1
auth-server.map.bigdata.logoutDomain=127.0.0.1


upload.tmp.dir=/tmp

logging.file.path=/tmp

server.port=8090

# ç»ä¸ç»å½å¼å³
oauth2.on-off=true
oauth2.client-id=kcde
oauth2.client-secret=kcde
oauth2.login-url=http://127.0.0.1:8003/platform/redirect/kcde?nonceId=%s
oauth2.admin=6313b274-0f3a-4718-9265-88c5b48de607
kcde.mg.index-url=http://ec.kcde.kscbigdata.cloud/#/main/infrastructure
kcde.dls.index-url=http://ec.kcde.kscbigdata.cloud/dlstudio/

forest.variables.oauth2TokenUrl=http://127.0.0.1:8003/oauth2Inner
forest.variables.kcdeAuthServerUrl=http://127.0.0.1:8090/authserver
forest.variables.velaApiUrl=http://vela-api-server-velacp.vela-system:8081
forest.connect-timeout=1800000
forest.read-timeout=1800000

# ????????????KDC???????
spring.security.oauth2.client.registration.kdc-idp.provider=kdc
spring.security.oauth2.client.registration.kdc-idp.client-id=kcde
spring.security.oauth2.client.registration.kdc-idp.client-secret=kcde
spring.security.oauth2.client.registration.kdc-idp.authorization-grant-type=client_credentials
spring.security.oauth2.client.registration.kdc-idp.redirect-uri=http://ec.kcde.kscbigdata.cloud/#/loginloading
spring.security.oauth2.client.registration.kdc-idp.scope=openid, profile
spring.security.oauth2.client.registration.kdc-idp.client-name=kde-client

spring.security.oauth2.client.provider.kdc.user-name-attribute=name
spring.security.oauth2.client.provider.kdc.authorization-uri=https://oct.authing.cn/oidc/auth
spring.security.oauth2.client.provider.kdc.token-uri=http://10.69.74.189:8003/oauth2Inner
spring.security.oauth2.client.provider.kdc.user-info-uri=http://10.69.74.189:8003/user
spring.security.oauth2.client.provider.kdc.jwk-set-uri=http://10.69.74.189:8003/oauth2Inner
#server.servlet.context-path=/
spring.security.oauth2.authserver.login-page-url=http://ec.kcde.kscbigdata.cloud/#/login
spring.security.oauth2.authserver.login-processing-url=/api/userLogin
spring.security.oauth2.authserver.staticResourceWhiteList= /**
spring.security.oauth2.authserver.fromRequestWhiteList=/api/code,/api/checkLoginType,/api/checkLogin,/api/oAuth2Login,/api/logout,/api/authenticate,/api/privilegeAuthentication,/key,/oauthLoginUrl,/app-token/*,/tenant-aksk/*,/tenant-token/*,/api/tenants/page
spring.security.oauth2.authserver.issuer=https://ec.kcde.kscbigdata.cloud/authserver
spring.security.oauth2.authserver.authServerUrl=http://ec.kcde.kscbigdata.cloud
spring.security.oauth2.authserver.ngServerUrl=http://authserver.kcdeapi.sdns.kscbigdata.cloud
spring.security.oauth2.authserver.logoutPageUrl=/api/logout

#çæ§ç¸å³--start
#management.server.port=8091
management.metrics.tags.application=AUTHSERVER
management.endpoints.web.exposure.include=prometheus
#management.metrics.distribution.slo.http.server.requests=1ms,5ms,10ms,50ms,100ms,200ms,500ms,1s,5s
#çæ§ç¸å³--end

logging.level.root: INFO
logging.level.org.springframework.web: INFO
logging.level.org.springframework.security: trace
logging.level.org.springframework.security.oauth2: trace
login.types=PASSWROD,CAS

rsa.public.key.path=classpath:public_key.pem
rsa.private.key.path=classpath:private_key.pem
