package com.ksyun.auth.server;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.core.oidc.OidcScopes;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since v1.8.0-2024/5/7
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class JpaTest {

    @Autowired
    private OAuth2AuthorizationService authorizationService;

    @Autowired
    private RegisteredClientRepository registeredClientRepository;

    private String encodePassword(String password) {

        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String password_one = encoder.encode(password);
        System.out.println("password_one: " + password_one);
        return password_one;
    }

    @Test
    public void test() {
        RegisteredClient registeredClient = registeredClientRepository.findByClientId("dls-client-test");
        System.out.println(registeredClient.getTokenSettings());
        System.out.println(registeredClient.getClientSettings());
    }

    @Test
    public void testSaveRegistrarClient() {

        RegisteredClient registrarClient = RegisteredClient.withId(UUID.randomUUID().toString())
                .clientId("registrar-client")
//                .clientSecret("registrar-client-secret")
                .clientSecret(encodePassword("registrar-client-secret"))
                .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
                .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
                .scope("client.create")
                .scope("client.read")
                .tokenSettings(TokenSettings.builder()
                        .accessTokenTimeToLive(Duration.ofMinutes(30))
                        .build()
                )
                .clientIdIssuedAt(Instant.now())
                .build();

        registeredClientRepository.save(registrarClient);

        RegisteredClient registeredClient = registeredClientRepository.findByClientId("registrar-client");
        System.out.println(registeredClient);
    }

    @Test
    public void testSaveJupyterhubClient() {

        RegisteredClient argoRegisteredClient = RegisteredClient.withId(UUID.randomUUID().toString())
                .clientId("jupyterhub-client")
//                .clientSecret("{noop}jupyterhub-secret")
                .clientSecret(encodePassword("jupyterhub-secret"))
                .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
                .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                .authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN)
                .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
                // 服务端注册的回调地址要保证和客户端的一致，注意127.0.0.1 和 localhost 是不一样的
                .redirectUri("http://oauth2-client:8090/authserver/login/oauth2/code/jupyterhub-oidc")
                .redirectUri("http://127.0.0.1:8090/authserver/login/oauth2/code/jupyterhub-oidc")
                .postLogoutRedirectUri("http://127.0.0.1:8090/authserver/logged-out")
                .scope(OidcScopes.OPENID)
                .scope(OidcScopes.PROFILE)
                // 关闭授权同意界面
                .clientSettings(ClientSettings.builder().requireAuthorizationConsent(false).build())
                .clientIdIssuedAt(Instant.now())
                .build();

        registeredClientRepository.save(argoRegisteredClient);

        RegisteredClient registeredClient = registeredClientRepository.findByClientId("jupyterhub-client");
        System.out.println(registeredClient);
    }

    @Test
    public void testSaveArgoClient() {

        RegisteredClient argoRegisteredClient = RegisteredClient.withId(UUID.randomUUID().toString())
                .clientId("argo-client")
//                .clientSecret("{noop}secret")
                .clientSecret(encodePassword("argo-secret"))
                .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
                .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                .authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN)
                .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
                // 服务端注册的回调地址要保证和客户端的一致，注意127.0.0.1 和 localhost 是不一样的
                .redirectUri("http://oauth2-client:8090/authserver/login/oauth2/code/argo-oidc")
                .redirectUri("http://127.0.0.1:8090/authserver/login/oauth2/code/argo-oidc")
                .postLogoutRedirectUri("http://127.0.0.1:8090/authserver/logged-out")
                .scope(OidcScopes.OPENID)
                .scope(OidcScopes.PROFILE)
                // 关闭授权同意界面
                .clientSettings(ClientSettings.builder().requireAuthorizationConsent(false).build())
                .clientIdIssuedAt(Instant.now())
                .build();

        registeredClientRepository.save(argoRegisteredClient);

        RegisteredClient registeredClient = registeredClientRepository.findByClientId("argo-client");
        System.out.println(registeredClient);
    }
    @Test
    public void testSavegateway_kcde() {

        RegisteredClient argoRegisteredClient = RegisteredClient.withId(UUID.randomUUID().toString())
                .clientId("kcde-gateway-Client-duli")
//                .clientSecret("{noop}secret")
                .clientSecret(encodePassword("kcde-gateway-Client-duli"))
                .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
                .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                .authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN)
                .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
                // 服务端注册的回调地址要保证和客户端的一致，注意127.0.0.1 和 localhost 是不一样的
                .redirectUri("http://ec.kcde.kscbigdata.cloud/gateway/login/oauth2/code/kcde-gateway-Client-duli")
                .postLogoutRedirectUri("http://127.0.0.1:8090/authserver/logged-out")
                .scope(OidcScopes.OPENID)
                .scope(OidcScopes.PROFILE)
                // 关闭授权同意界面
                .clientSettings(ClientSettings.builder().requireAuthorizationConsent(false).build())
                .clientIdIssuedAt(Instant.now())
                .build();
        registeredClientRepository.save(argoRegisteredClient);

        RegisteredClient registeredClient = registeredClientRepository.findByClientId("argo-client");
        System.out.println(registeredClient);
    }
    @Test
    public void testSaveDlsClient() {

        RegisteredClient argoRegisteredClient = RegisteredClient.withId(UUID.randomUUID().toString())
                .clientId("dls-client-test")
//                .clientSecret("{noop}secret-dls-test")
                .clientSecret(encodePassword("dls-client-secret"))
                .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
                .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                .authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN)
                .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
                // 服务端注册的回调地址要保证和客户端的一致，注意127.0.0.1 和 localhost 是不一样的
                .redirectUri("http://oauth2-client:9099/dls/login/oauth2/code/dls-client-test")
                .redirectUri("http://127.0.0.1:9099/dls/login/oauth2/code/dls-client-test")
                .postLogoutRedirectUri("http://127.0.0.1:9099/dls/logged-out")
                .scope(OidcScopes.OPENID)
                .scope(OidcScopes.PROFILE)
                // 关闭授权同意界面
                .clientSettings(ClientSettings.builder().requireAuthorizationConsent(false).build())
                .clientIdIssuedAt(Instant.now())
                .build();

        registeredClientRepository.save(argoRegisteredClient);

        RegisteredClient registeredClient = registeredClientRepository.findByClientId("dls-client-test");
        System.out.println(registeredClient.getTokenSettings());
    }

    @Test
    public void testObjectMapper() throws JsonProcessingException {
        var objectMapper = new ObjectMapper();

        String json = "{\"@class\":\"java.util.Collections$UnmodifiableMap\",\"settings.token.reuse-refresh-tokens\":true,\"settings.token.id-token-signature-algorithm\":[\"org.springframework.security.oauth2.jose.jws.SignatureAlgorithm\",\"RS256\"],\"settings.token.access-token-time-to-live\":{\"@class\":\"java.time.Duration\",\"seconds\":300,\"nano\":0},\"settings.token.access-token-format\":{\"@class\":\"org.springframework.security.oauth2.server.authorization.settings.OAuth2TokenFormat\",\"value\":\"self-contained\"},\"settings.token.refresh-token-time-to-live\":{\"@class\":\"java.time.Duration\",\"seconds\":3600,\"nano\":0},\"settings.token.authorization-code-time-to-live\":{\"@class\":\"java.time.Duration\",\"seconds\":300,\"nano\":0},\"settings.token.device-code-time-to-live\":{\"@class\":\"java.time.Duration\",\"seconds\":300,\"nano\":0}}";
        Map<String, Object> map = objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {
        });
        System.out.println(map);

//        // works
//        var list1 = Stream.of("a", "b", "c").collect(Collectors.toList());
//        var json1 = objectMapper.writeValueAsString(list1);
//        var deserealized1 = objectMapper.readValue(json1, Object.class);
//        System.out.println(deserealized1);
//
//        // fails
//        var list2 = Stream.of("a", "b", "c").toList();
//        var json2 = objectMapper.writeValueAsString(list2);
//        var deserealized2 = objectMapper.readValue(json2, Object.class);
//        System.out.println(deserealized2);
    }

    @Test
    public void testFindById() {
        OAuth2Authorization oAuth2Authorization = authorizationService.findById("f16943b2-d3ac-4652-9f5f-ff0439f187b9");
        System.out.println(oAuth2Authorization);
    }
}
