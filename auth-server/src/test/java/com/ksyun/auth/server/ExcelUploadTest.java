package com.ksyun.auth.server;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.io.FileInputStream;
import java.io.InputStream;

/**
 * 该单元测试用于测试文件上传接口，使用步骤
 * 1、先在认证白白名单增加上传接口
 * spring.security.oauth2.authserver.fromRequestWhiteList=...,/api/permissionGroups/upload
 * 2、AuthenticationInterceptor preHandle方法直接返回true
 * 3、从产品获取当前最新版本的权限点文档 放到auth-server/src/test/resources目录
 *  文档一般需要预处理
 *  1）删除前四列（从产品获取的文档需要这样操作）
 *  2）删除不需要的行
 *
 * 注意，该用例执行后，会直接全量覆盖数据库中的数据
 */
@SpringBootTest
@AutoConfigureMockMvc
class ExcelUploadTest {

    @Autowired
    private MockMvc mockMvc;


    @Test
    public void testSayHello() throws Exception {

//        String importFileName = "v1.8权限点.xlsx";
        String importFileName = "v2.1.0权限点.xlsx";
        InputStream inputStream = new FileInputStream("src/test/resources/" + importFileName);

        MockMultipartFile file = new MockMultipartFile(
                "file",                         // 参数名
                importFileName,                    // 文件名
                MediaType.APPLICATION_OCTET_STREAM_VALUE, // 文件类型
                inputStream                    // Excel文件内容
        );

        mockMvc.perform(MockMvcRequestBuilders.multipart("/api/permissionGroups/upload")
                        .file(file))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().string("Excel file uploaded successfully"));

    }
}
