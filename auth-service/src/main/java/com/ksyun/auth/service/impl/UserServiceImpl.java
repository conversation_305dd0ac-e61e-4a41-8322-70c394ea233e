package com.ksyun.auth.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.dao.*;
import com.ksyun.auth.dto.*;
import com.ksyun.auth.service.GroupUserService;
import com.ksyun.auth.service.RoleService;
import com.ksyun.auth.service.UserService;
import com.ksyun.auth.utils.PrivilegeUtils;
import com.ksyun.auth.utils.encrypt.RSAEncryption;
import com.ksyun.auth.vo.*;
import com.ksyun.common.constant.Constants;
import com.ksyun.common.constant.PropVoConstant;
import com.ksyun.common.entity.*;
import com.ksyun.common.enums.*;
import com.ksyun.common.exception.Assert;
import com.ksyun.common.exception.BusinessException;
import com.ksyun.common.utils.CollectionUtil;
import com.ksyun.common.utils.DateUtil;
import com.ksyun.common.utils.RsaAkSkUtils;
import com.ksyun.common.utils.Utils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 租户与子用户服务类
 */
@Service
@Slf4j
public class UserServiceImpl implements UserService {

    @Autowired
    UserMapper userMapper;
    @Autowired
    UserTenantMapper userTenantMapper;
    @Autowired
    GroupUserMapper groupUserMapper;
    @Autowired
    GroupMapper groupMapper;
    @Autowired
    UserPropsMapper userPropsMapper;
    @Autowired
    PrivilegeMapper privilegeMapper;
    @Autowired
    UserRoleMapper userRoleMapper;
    @Autowired
    GroupRoleMapper groupRoleMapper;
    @Autowired
    private RolePermissionGroupMapper rolePermissionGroupMapper;
    @Autowired
    private PrivilegePermissionGroupMapper privilegePermissionGroupMapper;
    @Autowired
    GroupUserService groupUserService;
    @Autowired
    RoleService roleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addUser(UserAddVo parameter) {
        LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
        //queryWrapper.eq(User::getTenantId, parameter.getTenantId());
        queryWrapper.eq(User::getName, parameter.getName());

        // 国网需求：应用软件系统应提供用户身份标识唯一性检查功能，保证系统中不存在重复标识的用户。
        queryWrapper.in(User::getStatus, UserStatusEnum.NORMAL.name(), UserStatusEnum.LOGOUT.name(), UserStatusEnum.SLEEP.name());
//      queryWrapper.eq(User::getSource, UserSourceEnum.LOCAL);

        Long userCount = userMapper.selectCount(queryWrapper);
        Assert.isTrue(userCount == null || userCount < 1, BusinessExceptionEnum.USER_EXISTED);

        // 保存用户信息
        User user = new User();
        user.setName(parameter.getName());
        user.setSource(parameter.getSource().name());
        //user.setTenantId(parameter.getTenantId());
        user.setStatus(UserStatusEnum.NORMAL.name());
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(user.getCreateTime());
        user.setRemark(parameter.getRemark());
        int i = userMapper.insert(user);
        if (i == 0) {
            throw new BusinessException("创建用户失败");
        }
        log.info("Saved User: user={}, source={}", user.getName(), user.getSource());
        // 保存用戶的属性信息
        if (!StringUtils.isEmpty(parameter.getAlias())) {
            updateUserProps(user.getId(), Constants.USER_PROP_KEY_ALIAS, parameter.getAlias());
        }
        if (!StringUtils.isEmpty(parameter.getEmail())) {
            updateUserProps(user.getId(), Constants.USER_PROP_KEY_EMAIL, parameter.getEmail());
        }
        if (!StringUtils.isEmpty(parameter.getPhone())) {
            updateUserProps(user.getId(), Constants.USER_PROP_KEY_PHONE, parameter.getPhone());
        }
        if (null != parameter.getDeadline()) {
            updateUserProps(user.getId(), Constants.USER_PROP_KEY_DEADLINE, String.valueOf(parameter.getDeadline()));
        }
        if (null != parameter.getDeadlineValidDay()) {
            updateUserProps(user.getId(), Constants.USER_PROP_KEY_DEADLINE_VALID_DAY, String.valueOf(parameter.getDeadlineValidDay()));
        }
        String password = parameter.getPassword();
        if (!StringUtils.isEmpty(password)) {
            // 密码加密保存到数据库
            updateUserProps(user.getId(), Constants.USER_PROP_KEY_PWD, DigestUtils.md5DigestAsHex(password.getBytes()));

            // 设置子账号登录时，是否重置密码
            updateUserProps(user.getId(), Constants.USER_PROP_KEY_RESET_PWD, String.valueOf(parameter.isResetPwdWhenFirstLogin()));
            // 设置密码生成时间
            updateUserProps(user.getId(), Constants.USER_PROP_KEY_PASSWORD_GENERATE_TIME, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }

        // 设置用户密级
        updateUserProps(user.getId(), Constants.USER_PROP_KEY_SECRET_LEVEL, String.valueOf(parameter.getSecretLevel()));
        updateUserProps(user.getId(), Constants.USER_PROP_KEY_LOGIN_TIME, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        Map<String, String> userAKSK = RsaAkSkUtils.getUserAKSK();
        saveUserAKSK(user, RsaAkSkUtils.USER_AK, userAKSK, RsaAkSkUtils.USER_SK);

        //绑定角色
        roleService.addBindUserAndRoles(Collections.singleton(user.getId()), parameter.getRoleIds());
    }

    @Override
    public Map<String, Object> addUser(UserAddOidcVo userAddOidcVo) {
        LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(User::getOidcId, userAddOidcVo.getOidcId());
        queryWrapper.eq(User::getSource, UserSourceEnum.CAS.name());
        queryWrapper.in(User::getStatus, UserStatusEnum.NORMAL.name(), UserStatusEnum.LOGOUT.name(), UserStatusEnum.SLEEP.name());

        HashMap<String, Object> map = new HashMap<>();
        User curUser = userMapper.selectOne(queryWrapper);
        if (curUser != null) {
            UserAkSkDto userAkSkDto = userPropsMapper.selectUserAKSK(curUser.getId());
            map.put("user", curUser);
            map.put("userLoginVo", UserLoginVo.builder()
                    .loginType(LoginTypeEnum.CAS.name())
                    .username(curUser.getName())
                    .password(userAkSkDto.getPassword())
                    .code("888") //验证码后门
                    .codeId("888")
                    .build());
            return map;
        }
//        LambdaQueryWrapper<User> queryNameWrapper = Wrappers.lambdaQuery();
//        queryNameWrapper.eq(User::getName, userAddOidcVo.getLoginName());
//        queryNameWrapper.eq(User::getSource, UserSourceEnum.CAS.name());
//        Long count = userMapper.selectCount(queryNameWrapper);
//        //判断用户是否存在
//        Assert.isTrue(count <= 0, BusinessExceptionEnum.USER_EXISTED);

        // 保存用户信息
        User user = new User();
        user.setName(userAddOidcVo.getLoginName());
        user.setSource(UserSourceEnum.CAS.name());
        user.setStatus(UserStatusEnum.NORMAL.name());
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(user.getCreateTime());
        user.setOidcId(userAddOidcVo.getOidcId());
        //user.setTenantId(1l);
        int i = userMapper.insert(user);
        if (i == 0) {
            throw new BusinessException("创建用户失败");
        }
        log.info("Saved User: user={}, source={}", user.getName(), user.getSource());
        // 保存用戶的属性信息
        if (!StringUtils.isEmpty(userAddOidcVo.getAlias())) {
            updateUserProps(user.getId(), Constants.USER_PROP_KEY_ALIAS, userAddOidcVo.getAlias());
        }
        if (!StringUtils.isEmpty(userAddOidcVo.getEmail())) {
            updateUserProps(user.getId(), Constants.USER_PROP_KEY_EMAIL, userAddOidcVo.getEmail());
        }
        if (!StringUtils.isEmpty(userAddOidcVo.getPhone())) {
            updateUserProps(user.getId(), Constants.USER_PROP_KEY_PHONE, userAddOidcVo.getPhone());
        }
        updateUserProps(user.getId(), Constants.USER_PROP_KEY_RESET_PWD, String.valueOf(false));
        String password = "123456";
        if (!StringUtils.isEmpty(password)) {
            // 密码加密保存到数据库
            updateUserProps(user.getId(), Constants.USER_PROP_KEY_PWD, DigestUtils.md5DigestAsHex(password.getBytes()));

            // 设置密码生成时间
            updateUserProps(user.getId(), Constants.USER_PROP_KEY_PASSWORD_GENERATE_TIME, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }

        // 设置用户密级
        updateUserProps(user.getId(), Constants.USER_PROP_KEY_LOGIN_TIME, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        Map<String, String> userAKSK = RsaAkSkUtils.getUserAKSK();
        saveUserAKSK(user, RsaAkSkUtils.USER_AK, userAKSK, RsaAkSkUtils.USER_SK);

        //绑定角色
        roleService.addBindUserAndRoles(user.getId(), userAddOidcVo.getRoleName());
        map.put("user", user);
        map.put("userLoginVo", UserLoginVo.builder()
                .loginType(LoginTypeEnum.CAS.name())
                .username(user.getName())
                .password(password)
                .code("888") //验证码后门
                .codeId("888")
                .build());
        return map;
    }

//    @Override
//    public void addTenant(TenantAddVo parameter) {
//        LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
//        queryWrapper.eq(User::getName, parameter.getName());
//        queryWrapper.eq(User::getStatus, UserStatusEnum.NORMAL.name());
//        User exists = userMapper.selectOne(queryWrapper);
//        Assert.isNull(exists, BusinessExceptionEnum.USER_EXISTED);
//
//        User tenant = new User();
//        tenant.setName(parameter.getName());
//        tenant.setSource(UserSourceEnum.LOCAL.name());
//        tenant.setStatus(UserStatusEnum.NORMAL.name());
//        tenant.setCreateTime(LocalDateTime.now());
//        tenant.setUpdateTime(tenant.getCreateTime());
//
//        // 保存租户信息
//        int i = userMapper.insert(tenant);
//        if (i == 0) {
//            throw new BusinessException("创建租户失败");
//        }
//        // 更新租户的租户ID字段
//        User tenant1 = new User();
//        tenant1.setTenantId(tenant.getId());
//        LambdaUpdateWrapper<User> userUpdateWrapper = Wrappers.lambdaUpdate();
//        userUpdateWrapper.eq(User::getId, tenant.getId());
//        userMapper.update(tenant1, userUpdateWrapper);
//        // 保存租户属性
//        saveTenantProps(parameter, tenant);
//    }

//    private void saveTenantProps(TenantAddVo parameter, User tenant) {
//        Map<String, String> map = new HashMap<>();
//        if (!StringUtil.isEmpty(parameter.getPhone())) {
//            map.put(Constants.USER_PROP_KEY_PHONE, parameter.getPhone());
//        }
//        if (!StringUtil.isEmpty(parameter.getEmail())) {
//            map.put(Constants.USER_PROP_KEY_EMAIL, parameter.getEmail());
//        }
//
//        // 设置用户密级
//        map.put(Constants.USER_PROP_KEY_SECRET_LEVEL, String.valueOf(1));
//        if (!StringUtils.isEmpty(parameter.getPassword())) {
//            // 密码加密后保存到数据库
//            map.put(Constants.USER_PROP_KEY_PWD, DigestUtils.md5DigestAsHex(parameter.getPassword().getBytes()));
//        }
//        // 保存用户级AkSk
//        Map<String, String> userAKSK = RsaAkSkUtils.getUserAKSK();
//        map.put(RsaAkSkUtils.USER_AK, userAKSK.get(RsaAkSkUtils.USER_AK));
//        map.put(RsaAkSkUtils.USER_SK, userAKSK.get(RsaAkSkUtils.USER_SK));
//        map.put("userAkSk-status", AkSkStatusEnum.NORMAL.name());
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        map.put("userAk.create-time", sdf.format(new Date()));
//
//        for (Map.Entry<String, String> prop : map.entrySet()) {
//            UserProps userProps = new UserProps();
//            userProps.setPropValue(prop.getKey());
//            userProps.setPropValue(prop.getValue());
//            userProps.setUserId(tenant.getId());
//            userPropsMapper.insert(userProps);
//        }
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(UserVo parameter) {
        Optional<UserVo> existsUserOptional = getUserById(parameter.getId());
        Assert.isTrue(existsUserOptional.isPresent(), BusinessExceptionEnum.USER_NOT_FOUND);
        UserVo existsUser = existsUserOptional.get();
        log.info("existsUser={}", JSON.toJSONString(existsUser));
        if(!StringUtils.isEmpty(parameter.getRemark())){
            existsUser.setRemark(parameter.getRemark());
        }
        //1. 修改用户属性
        if (!StringUtils.isEmpty(parameter.getAlias())) {
            updateUserProps(parameter.getId(), Constants.USER_PROP_KEY_ALIAS, parameter.getAlias());
        }
        if (!StringUtils.isEmpty(parameter.getPassword())) {
            String password = parameter.getPassword();
            String name = existsUser.getName();
            if (password.contains(name)) {
                throw new RuntimeException("密码不能包含用户名");
            }
            updateUserProps(parameter.getId(), Constants.USER_PROP_KEY_PWD, DigestUtils.md5DigestAsHex(parameter.getPassword().getBytes()));
        }
        if (null != parameter.getEmail()) {
            updateUserProps(parameter.getId(), Constants.USER_PROP_KEY_EMAIL, parameter.getEmail());
        }
        if (null != parameter.getPhone()) {
            updateUserProps(parameter.getId(), Constants.USER_PROP_KEY_PHONE, parameter.getPhone());
        }
        if (null != parameter.getDeadline()) {
            updateUserProps(parameter.getId(), Constants.USER_PROP_KEY_DEADLINE, String.valueOf(parameter.getDeadline()));
        }
        if (null != parameter.getDeadline()) {
            updateUserProps(parameter.getId(), Constants.USER_PROP_KEY_DEADLINE_VALID_DAY, String.valueOf(parameter.getDeadlineValidDay()));
        }
        //2.修改用户分组
        if (!CollectionUtils.isEmpty(parameter.getGroupIds())) {
            updateUserGroups(parameter.getId(), getGroupIds(existsUser.getGroups()), parameter.getGroupIds());
        }

        //3.修改用户
        updateUser(existsUser);

        //绑定角色
        if (!CollectionUtils.isEmpty(parameter.getRoleIds())) {
            roleService.addBindOrDeleteBindUserAndRoles(Collections.singleton(existsUser.getId()), parameter.getRoleIds());
        }
    }

    private void updateUser(UserVo existsUser) {
        //Assert.notNull(existsUser.getTenant(), BusinessExceptionEnum.PARAM_INVALID, "租户信息为空");
        User user = new User();
        user.setName(existsUser.getName());
        user.setSource(existsUser.getSource().name());
        user.setUpdateTime(LocalDateTime.now());
        user.setStatus(existsUser.getStatus().name());
        user.setRemark(existsUser.getRemark());
        //user.setTenantId(existsUser.getTenant().getId());
        user.setCreateTime(DateUtil.date2LocalDateTime(existsUser.getCreateTime()));
        LambdaUpdateWrapper<User> userUpdateWrapper = Wrappers.lambdaUpdate();
        userUpdateWrapper.eq(User::getId, existsUser.getId());
        userMapper.update(user, userUpdateWrapper);
    }

    private void updateUserGroups(Long userId, Set<Long> fromGroups, Set<Long> toGroups) {
        Set<Long> userIds = Collections.singleton(userId);
        Set<Long> unchangedGroups = !fromGroups.isEmpty() && !toGroups.isEmpty() ? cleanUnchangedGroupIds(fromGroups, toGroups) : Collections.emptySet();
        int deletedGroups = 0, insertedGroups = 0;
        if (!fromGroups.isEmpty()) {
            LambdaQueryWrapper<GroupUser> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(CollectionUtil.isNotEmpty(userIds), GroupUser::getUserId, userIds);
            queryWrapper.in(CollectionUtil.isNotEmpty(fromGroups), GroupUser::getGroupId, fromGroups);
            deletedGroups = groupUserMapper.delete(queryWrapper);
        }
        if (CollectionUtil.isNotEmpty(toGroups)) {
            List<GroupUser> groupUserList = new ArrayList<>();
            List<UserGroupPairVo> userGroupPairVoList = UserGroupBindVo.newUserGroupPairs(userIds, new HashSet<>(toGroups));
            if (CollectionUtil.isNotEmpty(userGroupPairVoList)) {
                userGroupPairVoList.forEach(pair -> {
                    GroupUser groupUser = new GroupUser();
                    groupUser.setUserId(pair.getUserId()).setGroupId(pair.getGroupId());
                    groupUserList.add(groupUser);
                });
            }
            groupUserService.saveBatch(groupUserList);

        }
        log.info("Updated User Groups: unchanged={}, deleted={}, inserted={}", unchangedGroups.size(), deletedGroups, insertedGroups);
    }

    /**
     * 去除不需要修改的用户组
     */
    private Set<Long> cleanUnchangedGroupIds(Set<Long> from, Set<Long> to) {
        Set<Long> copy = new HashSet<>(from);
        copy.retainAll(to);
        if (!copy.isEmpty()) {
            from.removeAll(copy);
            to.removeAll(copy);
        }
        return copy;
    }

    /**
     * 逻辑删除
     * 有唯一索引，如果已经有一条被删除的记录，先清掉无效的记录，再进行逻辑删除
     *
     * @param userDeleteParameter
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(UserDeleteVo userDeleteParameter) {
        Set<Long> userIds = userDeleteParameter.getUserIds();
        if (CollectionUtil.isNotEmpty(userIds)) {
            userIds.forEach(userId -> {
                Optional<UserVo> user = getUserById(userId);
                Assert.isTrue(user.isPresent() && !UserVo.Status.DELETED.toString().equals(user.get().toString()), BusinessExceptionEnum.USER_NOT_FOUND);
            });

            LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(User::getId, userIds);
            List<User> users = userMapper.selectList(queryWrapper);
            if (!CollectionUtils.isEmpty(users)) {
                Set<Long> deleteUserIds = users.stream().map(u -> u.getId()).collect(Collectors.toSet());

                for (User u : users) {
                    LambdaQueryWrapper<User> deleteWrapper = Wrappers.lambdaQuery();
                    deleteWrapper.eq(User::getStatus, UserVo.Status.DELETED.toString());
                    deleteWrapper.eq(User::getName, u.getName());
                    userMapper.delete(deleteWrapper);

                    LambdaQueryWrapper<UserTenant> deleteTenantWrapper = Wrappers.lambdaQuery();
                    deleteTenantWrapper.eq(UserTenant::getUserId, u.getId());
                    userTenantMapper.delete(deleteTenantWrapper);
                }
                userMapper.batchDelete(deleteUserIds);
            }
        }
    }

    @Override
    public Page<BaseUser> page(UserQueryVo parameter) {
        // 分页查询用户
        List<BaseUser> data = userMapper.selectBaseUserByCondition(parameter);
        int count = userMapper.selectBaseUserCountByCondition(parameter);
        Page<BaseUser> page = new Page<>();
        page.setRecords(data);
        page.setTotal(count);
        page.setSize(parameter.getPageSize());
        page.setCurrent(parameter.getPageNo());
        return page;
    }

    @Override
    public Optional<List<UserVo>> getUserByName(String userName) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getName, userName);
        queryWrapper.in(User::getStatus, UserStatusEnum.NORMAL.name(), UserStatusEnum.LOGOUT.name(), UserStatusEnum.SLEEP.name());
        List<User> list = userMapper.selectList(queryWrapper);
        if (list != null) {
            List<UserVo> userVoList = new ArrayList<>();
            for (User user : list) {
                UserVo userVo = convertToVo(user);
                userVoList.add(userVo);
            }
            return Optional.of(userVoList);
        }
        return Optional.empty();
    }

//    /**
//     * 根据租户id和用户id查找用户
//     *
//     * @param tenantId 租户ID
//     * @param userId   用户ID
//     * @return 用户信息
//     */
//    private UserVo lookupUserByTenantIdAndUserId(Long tenantId, Long userId) {
//        User user = findUser(null, tenantId, userId);
//        if (user == null) {
//            return null;
//        }
//        return convertToVo(user);
//    }

    /**
     * 根据用户ID查询用户属性信息
     *
     * @param userId 用户ID
     */
    private List<UserProps> lookupUserPropsById(Long userId) {
        LambdaQueryWrapper<UserProps> queryPropWrapper = Wrappers.lambdaQuery();
        queryPropWrapper.eq(UserProps::getUserId, userId);
        return userPropsMapper.selectList(queryPropWrapper);
    }

    /**
     * 获取用户组ID
     */
    private Set<Long> getGroupIds(List<InnerGroupDto> groups) {
        return CollectionUtils.isEmpty(groups) ? Collections.emptySet() :
                groups.stream().map(InnerGroupDto::getId).collect(Collectors.toSet());
    }

    /**
     * 修改密码
     */
    @Override
    public void updatePassword(UserPasswordVo parameter, boolean isRestPasswordFlag) {
        Assert.isTrue(!parameter.getNewPassword().equals(parameter.getOldPassword()), BusinessExceptionEnum.USER_PASSWORD_CONSISTENT);
        //获取用户对应的属性列表
        List<UserProps> userProps = lookupUserPropsById(parameter.getUserId());
        //用户属性列表转换成Map
        Map<String, String> userPropsMap = userProps.stream().collect(Collectors.toMap(UserProps::getPropKey, UserProps::getPropValue));
        //加密新密码
        String newPassword = DigestUtils.md5DigestAsHex(parameter.getNewPassword().getBytes());
        //获取旧密码
        String password = userPropsMap.get(Constants.USER_PROP_KEY_PWD);
        Assert.isTrue(DigestUtils.md5DigestAsHex(parameter.getOldPassword().getBytes()).equals(password), BusinessExceptionEnum.USER_PASSWORD_ERROR);
        Assert.isTrue(!parameter.getNewPassword().equals(userPropsMap.get(Constants.USER_PROP_KEY_PWD)), BusinessExceptionEnum.USER_PASSWORD_CONSISTENT);
        updateUserProps(parameter.getUserId(), Constants.USER_PROP_KEY_PWD, newPassword);

        if (isRestPasswordFlag) {
            log.info("updatePassword isRestPasswordFlag");
            updateUserProps(parameter.getUserId(), Constants.USER_PROP_KEY_RESET_PWD, "false");
        }
        //password-generate-time
        updateUserProps(parameter.getUserId(), Constants.USER_PROP_KEY_PASSWORD_GENERATE_TIME, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    }

    @Override
    public void getUserExist(Set<Long> userIds) {
        // 检查用户是否存在
        UserQueryVo userQueryParameter = new UserQueryVo();
        userQueryParameter.setUserIds(userIds);
        //userQueryParameter.setTenantId(tenetId);
        //userQueryParameter.setIsNotDisplayTenant(false);
        int count = userMapper.selectBaseUserCountByCondition(userQueryParameter);
        Assert.isTrue(Objects.equals(count, userIds.size()), BusinessExceptionEnum.USER_NOT_FOUND);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void subAccountResetPasswordFirstLogin(SubAccountPasswordResetVo parameter) {
        // 检查用户输入的新密码与确认密码是否一致
        Assert.isTrue(Objects.equals(parameter.getNewPassword(), parameter.getConfirmPassword()), BusinessExceptionEnum.USER_PASSWORD_NOT_CONSISTENT);

        // 检查用户原来的密码与新密码是否一致
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        //queryWrapper.eq("tenant_id", parameter.getTenantId());
        queryWrapper.eq("id", parameter.getUserId());
        User user = userMapper.selectOne(queryWrapper);

        List<UserProps> propVoList = getPropVosByUser(user);
        String password = parameter.getNewPassword();
        String md5NewPassword = DigestUtils.md5DigestAsHex(password.getBytes());
        Assert.isTrue(!md5NewPassword.equals(getProp(propVoList, Constants.USER_PROP_KEY_PWD)), BusinessExceptionEnum.USER_PASSWORD_CONSISTENT);

        // 更新密码
        updateUserProps(parameter.getUserId(), Constants.USER_PROP_KEY_PWD, md5NewPassword);

        // 更新重置密码的属性值为false
        if (Boolean.parseBoolean(getProp(propVoList, Constants.USER_PROP_KEY_RESET_PWD).toString())) {
            updateUserProps(parameter.getUserId(), Constants.USER_PROP_KEY_RESET_PWD, String.valueOf(false));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPassword(SubAccountPasswordResetVo parameter) {
        // 检查用户输入的新密码与确认密码是否一致
        Assert.isTrue(Objects.equals(parameter.getNewPassword(), parameter.getConfirmPassword()), BusinessExceptionEnum.USER_PASSWORD_NOT_CONSISTENT);

        // 检查用户原来的密码与新密码是否一致
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        //queryWrapper.eq("tenant_id", parameter.getTenantId());
        queryWrapper.eq("id", parameter.getUserId());
        User user = userMapper.selectOne(queryWrapper);

        List<UserProps> propVoList = getPropVosByUser(user);
        String password = parameter.getNewPassword();
        String md5NewPassword = DigestUtils.md5DigestAsHex(password.getBytes());
        Assert.isTrue(!md5NewPassword.equals(getProp(propVoList, Constants.USER_PROP_KEY_PWD)), BusinessExceptionEnum.USER_PASSWORD_CONSISTENT);

        // 更新密码
        updateUserProps(parameter.getUserId(), Constants.USER_PROP_KEY_PWD, md5NewPassword);

        // 更新重置密码的属性值为false
        if (Boolean.parseBoolean(getProp(propVoList, Constants.USER_PROP_KEY_RESET_PWD).toString())) {
            updateUserProps(parameter.getUserId(), Constants.USER_PROP_KEY_RESET_PWD, String.valueOf(false));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserSecretLevel(SecretLevelSetVo parameter) {
        if (null != parameter && CollectionUtil.isNotEmpty(parameter.getUserIds())) {
            UserProps userProps = new UserProps();
            userProps.setPropValue(String.valueOf(parameter.getSecretLevel()));
            LambdaQueryWrapper<UserProps> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(UserProps::getUserId, parameter.getUserIds());
            queryWrapper.eq(UserProps::getPropKey, Constants.USER_PROP_KEY_SECRET_LEVEL);
            userPropsMapper.update(userProps, queryWrapper);
        }
    }

//    @Override
//    public UserVo getTenantByName(String tenantName) {
//        User user = findUser(tenantName, null, null);
//        if (user == null) {
//            return null;
//        }
//        return convertToVo(user);
//    }

    private User findUser(String userName, Long userId) {
        LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(!StringUtils.isEmpty(userName), User::getName, userName);
        //queryWrapper.eq(null != tenantId, User::getTenantId, tenantId);
        queryWrapper.eq(null != userId, User::getId, userId);
        queryWrapper.in(User::getStatus, UserStatusEnum.NORMAL.name(), UserStatusEnum.SLEEP.name(), UserStatusEnum.LOGOUT.name());
        return userMapper.selectOne(queryWrapper);
    }


    private UserVo convertToVo(User user) {
        LambdaQueryWrapper<GroupUser> groupUserQueryWrapper = Wrappers.lambdaQuery();
        groupUserQueryWrapper.eq(GroupUser::getUserId, user.getId());
        List<InnerGroupDto> innerGroupList = groupUserMapper.selectList(groupUserQueryWrapper)
                .stream()
                .map(g -> {
                    Group group = groupMapper.selectById(g.getGroupId());
                    InnerGroupDto innerGroup = new InnerGroupDto();
                    innerGroup.setName(group.getName());
                    innerGroup.setId(group.getId());
                    return innerGroup;
                }).collect(Collectors.toList());

        //User tenant = userMapper.selectById(user.getTenantId());
        List<UserProps> propVoList = getPropVosByUser(user);
        Map<String, String> propMap = propVoList.stream().collect(Collectors.toMap(UserProps::getPropKey, UserProps::getPropValue));

        UserVo userVo = new UserVo();
        BeanUtils.copyProperties(user, userVo);
        //InnerTenantDto innerTenant = new InnerTenantDto();
        //BeanUtils.copyProperties(tenant, innerTenant);
        userVo.setSource(UserSourceEnum.valueOf(user.getSource()));
        userVo.setGroups(innerGroupList);
        userVo.setRoleIds(lookupUserRoleIds(user.getId()));
        //userVo.setTenant(innerTenant);
        userVo.setProps(propVoList.stream().map(u -> new PropVo(u.getPropKey(), u.getPropValue())).collect(Collectors.toList()));
        userVo.setSecretLevel(Integer.parseInt(propMap.getOrDefault(Constants.USER_PROP_KEY_SECRET_LEVEL, "0")));
        userVo.setCreateTime(DateUtil.localDateTime2Date(user.getCreateTime()));
        userVo.setUpdateTime(DateUtil.localDateTime2Date(user.getUpdateTime()));
        userVo.setAlias(propMap.getOrDefault(Constants.USER_PROP_KEY_ALIAS, ""));
        userVo.setPhone(propMap.getOrDefault(Constants.USER_PROP_KEY_PHONE, ""));
        userVo.setEmail(propMap.getOrDefault(Constants.USER_PROP_KEY_EMAIL, ""));
        return userVo;
    }

    /**
     * 用户签权和项目签权， 当传递参数projectId不为空时，进行项目签权
     * add by xiawg  2020-05-28
     */
    private CheckPrivilegeResultVo hasPrivilege(User user, Long privilegeId) {
        // 获取版本信息 判断当前版本是否有权限

//        //判断当前用户是否为租户
//        if (user.getId().equals(user.getTenantId())) {
//            log.info("lookupCurrentUserIsPrivilege Request Parameter userId={} Am Tenant,PrivilegeId={}", user.getId(), privilegeId);
//            return CheckPrivilegeResultVo.success();
//        } else {
        Set<Long> roleIds = new HashSet<>(lookupUserRoleIds(user.getId()));
        log.info("lookupCurrentUserIsPrivilege Request Parameter userId={}, roleIds={}", user.getId(), roleIds);
        // 用户没有任何角色的时候，返回空的集合
        if (roleIds.isEmpty()) {
            return CheckPrivilegeResultVo.fail("no roles found for userId: " + user.getId());
        }
//        }
        //根据角色Id集合查询权限组Id集合
        LambdaQueryWrapper<RolePermissionGroup> rolePermissionGroupLambdaQueryWrapper = Wrappers.<RolePermissionGroup>lambdaQuery()
                .in(RolePermissionGroup::getRoleId, roleIds);
        List<RolePermissionGroup> rolePermissionGroups = rolePermissionGroupMapper.selectList(rolePermissionGroupLambdaQueryWrapper);
        if (Objects.isNull(rolePermissionGroups) || rolePermissionGroups.isEmpty()) {
            return CheckPrivilegeResultVo.fail("no permission groups found for roles: " + roleIds.toString());
        }
        Set<Long> permissionGroupIds = rolePermissionGroups.stream().map(RolePermissionGroup::getPermissionGroupId)
                .collect(Collectors.toSet());
        //根据权限id和权限组Id集合查询是否有权限
        LambdaQueryWrapper<PrivilegePermissionGroup> privilegePermissionGroupLambdaQueryWrapper = Wrappers.<PrivilegePermissionGroup>lambdaQuery()
                .in(PrivilegePermissionGroup::getPermissionGroupId, permissionGroupIds)
                .eq(PrivilegePermissionGroup::getPrivilegeId, privilegeId);
        Long privilegeCount = privilegePermissionGroupMapper.selectCount(privilegePermissionGroupLambdaQueryWrapper);
        if (privilegeCount == 0) {
            return CheckPrivilegeResultVo.fail("no role assigned the privilegeId : " + privilegeId);
        }
        return CheckPrivilegeResultVo.success();
    }

    /**
     * 获取子账号的角色列表
     */
    private Set<Long> lookupUserRoleIds(Long userId) {
        QueryWrapper<GroupUser> groupUserQueryWrapper = new QueryWrapper<>();
        groupUserQueryWrapper.eq("user_id", userId);
        Set<Long> groupIds = groupUserMapper.selectList(groupUserQueryWrapper)
                .stream().map(GroupUser::getGroupId).collect(Collectors.toSet());
        Set<Long> groupRoleIds = Sets.newHashSet();
        if (CollectionUtil.isNotEmpty(groupIds)) {
            QueryWrapper<GroupRole> groupRoleQueryWrapper = new QueryWrapper<>();
            groupRoleQueryWrapper.in("group_id", groupIds);
            groupRoleIds.addAll(groupRoleMapper.selectList(groupRoleQueryWrapper)
                    .stream().map(GroupRole::getRoleId).collect(Collectors.toSet()));
        }
        // 查询用户本身的角色
        QueryWrapper<UserRole> userRoleQueryWrapper = new QueryWrapper<>();
        userRoleQueryWrapper.eq("user_id", userId);
        Set<Long> userRoleIds = userRoleMapper.selectList(userRoleQueryWrapper)
                .stream().map(UserRole::getRoleId).collect(Collectors.toSet());
        groupRoleIds.addAll(userRoleIds);
        return groupRoleIds;
    }

    private Set<Long> matchPrivilege(String ak, String url) {
        //精确匹配
        QueryWrapper<Privilege> privilegeQueryWrapper = new QueryWrapper<>();
        //privilegeQueryWrapper.eq("ak", ak);
        privilegeQueryWrapper.like("url", url);
        Set<Long> privilegeIds = privilegeMapper.selectList(privilegeQueryWrapper)
                .stream().map(Privilege::getId).collect(Collectors.toSet());
        if (!CollectionUtil.isNotEmpty(privilegeIds)) {
            //变量路径匹配
            QueryWrapper<Privilege> privilegeQueryWrapper1 = new QueryWrapper<>();
            // privilegeQueryWrapper1.eq("ak", ak);
            privilegeQueryWrapper1.like("url", "%{%");
            List<Privilege> privilegeList = privilegeMapper.selectList(privilegeQueryWrapper1);
            ImmutablePair<String, String> urlPairs = Utils.parse(url);
            String uri = urlPairs.getKey();
            String method = urlPairs.getValue();
            log.info("matchPrivilege privilegeList={},uri={},method={}", JSON.toJSONString(privilegeList), uri, method);
            AntPathMatcher pathMatcher = new AntPathMatcher();
            for (Privilege entity : privilegeList) {
                String[] urls = entity.getUrl().split(";");
                for (String urlPattern : urls) {
                    ImmutablePair<String, String> urlPair = Utils.parse(urlPattern);
                    if (!urlPair.getValue().equals(method)) {
                        continue;
                    }
                    if (pathMatcher.match(urlPair.getKey(), uri)) {
                        privilegeIds.add(entity.getId());
                        break;
                    }
                }
            }
        }
        return privilegeIds;
    }

    @Override
    public AuthUser getUserByLoginVo(String userName, String password, String loginType, String rsaPrivateKey, String source) {
        Assert.notNull(userName, BusinessExceptionEnum.PARAM_INVALID);
        Assert.notNull(password, BusinessExceptionEnum.PARAM_INVALID);
        //String cleartextPasswords = new RSAEncryption().decryptByPrivateKey(passwordVo, rsaPrivateKey);//解密
        String encryptPassword = DigestUtils.md5DigestAsHex(password.getBytes());
        //兼容密码本身就是md5的情况[第三方登录时,从数据库查的密码是md5的.所以需要兼容这种情况]
//        String source = null;
//        if (loginType.equalsIgnoreCase(LoginTypeEnum.PASSWORD.name())) {
//            source = UserSourceEnum.LOCAL.name();
//        } else if (loginType.equalsIgnoreCase(LoginTypeEnum.CAS.name())) {
//            source = UserSourceEnum.CAS.name();
//        } else {
//            Assert.notNull(source, BusinessExceptionEnum.LOGINTYPE_NOT_EXISTED);
//        }
        Set<String> sourceList = UserSourceEnum.getAllUserSource();
        Assert.isTrue(sourceList.contains(source), BusinessExceptionEnum.SOURCE_NOT_EXISTED);
        AuthUser authUser;
        if(source.equalsIgnoreCase(UserSourceEnum.CAS.name())){
            Set<String> set = new HashSet<String>();
            set.add(source);
            authUser = userMapper.selectByUserNameAndPassword2(userName, encryptPassword, password, set);
        }else {
            authUser = userMapper.selectByUserNameAndPassword2(userName, encryptPassword, password, sourceList);
        }
        //判断当用户用小米人账号通过平台登录时.提示用户用小米人登录
        //casUserPlatformLoing(userName, source);
        log.info("Auth User {} ", authUser);
        Assert.notNull(authUser, BusinessExceptionEnum.USER_USERNAME_OR_PASSWORD_ERROR);
        List<KcdeTenantVo> tenants = userTenantMapper.getUserTenants(authUser.getId());
        log.info("Tenants size {} ", tenants != null ? tenants.size() : 0);
        if (tenants != null) {
            tenants.forEach(t -> {
                log.info("Tenant id {}, name {}, status {} ", t.getId(), t.getName(), t.getStatus());
                authUser.attachTenant(new AuthUser.InnerTenant(t.getId(), t.getName(), t.getStatus()));
            });
        }
        authUser.setSystemType(MDC.get(Constants.SYSTEM_TYPE));
        return authUser;
    }

    private void casUserPlatformLoing(String userName, String source) {
        LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(User::getName, userName);
        queryWrapper.in(User::getStatus, UserStatusEnum.NORMAL.name(), UserStatusEnum.LOGOUT.name(), UserStatusEnum.SLEEP.name());
        queryWrapper.in(User::getSource, UserSourceEnum.LOCAL, UserSourceEnum.CAS);
        List<User> userList = userMapper.selectList(queryWrapper);
        if (userList.size() == 1 && userList.get(0).getSource().equals(UserSourceEnum.CAS.name()) && source.equalsIgnoreCase(UserSourceEnum.LOCAL.name())) {
            throw new BusinessException("小米人账号请通过小米人登录");
        }
    }

    @Override
    public AuthUser getUserBySourceAndOidcId(String source, String oidcId) {
        AuthUser authUser = userMapper.selectBySourceAndOidcId(source, oidcId);
        Assert.notNull(authUser, BusinessExceptionEnum.USER_USERNAME_OR_PASSWORD_ERROR);
        List<KcdeTenantVo> tenants = userTenantMapper.getUserTenants(authUser.getId());
        if (tenants != null) {
            tenants.forEach(t -> authUser.attachTenant(new AuthUser.InnerTenant(t.getId(), t.getName(), t.getStatus())));
        }
        authUser.setSystemType(MDC.get(Constants.SYSTEM_TYPE));
        return authUser;
    }

    @Override
    public CheckPrivilegeResultVo getCurrentUserIsPrivilege(String ak, Long projectId, Long userId, String url, String network) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            return CheckPrivilegeResultVo.fail("invalid userId:" + userId);
        }
        // 根据ak 与url 查询当前权限点
        Set<Long> privilegeIds = matchPrivilege(ak, url);
        log.info("ak={}, url={}, ==> privilegeIds: ={}", ak, url, privilegeIds);
        for (Long privilegeId : privilegeIds) {
            if (hasPrivilege(user, privilegeId).isPassed()) {
                return CheckPrivilegeResultVo.success();
            }
        }
        return CheckPrivilegeResultVo.fail(com.ksyun.auth.client.Constants.NOT_FOUND_NO_PRIVILEGE);
    }

    /*protected void updateIamId(String iamId, User user) {
        if (!StringUtils.isEmpty(iamId) && !Objects.equals("0", iamId)) {
            log.info("update IamId userId={}, iamId={}", user.getId(), "");
            userMapper.updateById(user);
        }
    }*/

    protected Map<String, String> getUserAKSK() {
        Map<String, String> userAKSK = new HashMap<>();
        // 生成ak
        String userAk = getDigestResult(UUID.randomUUID().toString().replace("-", ""));
        // 生成sk
        String userSk = getDigestResult(UUID.randomUUID().toString().replace("-", ""));
        userAKSK.put(Constants.USER_AK, userAk);
        userAKSK.put(Constants.USER_SK, userSk);
        log.info("Request RsaAkSkUtils Success Response AK={},SK={}", userAk, userSk);
        return userAKSK;
    }

    private String getDigestResult(String str) {
        try {
            MessageDigest mDigest = MessageDigest.getInstance("SHA-256");
            mDigest.update(str.getBytes());
            BigInteger bigInt = new BigInteger(1, mDigest.digest());
            return bigInt.toString(16);
        } catch (NoSuchAlgorithmException e) {
            log.error("getDigestResult error message={}", e.getMessage());
        }
        return str;
    }

    private List<UserProps> getPropVosByUser(User user) {
        QueryWrapper<UserProps> userPropsQueryWrapper = new QueryWrapper<>();
        userPropsQueryWrapper.eq("user_id", user.getId());
        return userPropsMapper.selectList(userPropsQueryWrapper);
    }

    private Object getProp(List<UserProps> props, String key) {
        Optional<UserProps> optional = props.stream().filter(prop -> prop.getPropKey().equals(key)).findAny();
        return optional.<Object>map(UserProps::getPropValue).orElse(null);
    }

    private void updateUserProps(Long userId, String propKey, String propValue) {
        LambdaQueryWrapper<UserProps> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserProps::getPropKey, propKey);
        queryWrapper.eq(UserProps::getUserId, userId);
        int delete = userPropsMapper.delete(queryWrapper);
        log.info("updateUserProps delete userId={},propKey={},propValue={},result={}", userId, propKey, propValue, delete);

        UserProps userProps = new UserProps(userId, propKey, propValue);
        int insert = userPropsMapper.insert(userProps);
        log.info("updateUserProps insert userId={},propKey={},propValue={},result={}", userId, propKey, propValue, insert);
    }

    public Optional<UserVo> getUserById(Long userId) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", userId);
        User user = userMapper.selectOne(queryWrapper);
        if (user != null) {
            return Optional.of(convertToVo(user));
        }
        return Optional.empty();
    }

//    private AuthUser saveOrUpdateForUser(UserVo userVo, boolean syncFlag) {
//        User user = getUserByUserVo(userVo, syncFlag);
//        log.info("saveOrUpdateForUser Parameter={},Result={}", JSON.toJSONString(userVo), JSON.toJSONString(user));
//        if (null == user) {
//            user = new User();
//            user.setName(userVo.getName());
//            user.setSource(userVo.getSource().name());
//            user.setStatus(userVo.getStatus().name());
//            user.setCreateTime(LocalDateTime.now());
//            user.setUpdateTime(LocalDateTime.now());
//            user.setTenantId(userVo.getTenant().getId());
//            if (userMapper.insert(user) == 0) {
//                log.error("User Already Exists: name={}", user.getName());
//                return null;
//            }
//
//            saveUserAKSK(user, Constants.USER_AK, getUserAKSK(), Constants.USER_SK);
//            saveUserProps(userVo, user.getId());
//            log.info("saveOrUpdateForUser name={},iamId={},syncFlag={}", userVo.getName(), "", syncFlag);
//        } else {
//            UserAkSkDto userAkSk = userPropsMapper.selectUserAKSK(user.getId());
//            if (userAkSk == null) {
//                saveUserAKSK(user, Constants.USER_AK, getUserAKSK(), Constants.USER_SK);
//            }
//
//            updateUserProps(userVo, user.getId());
//        }
//        return userMapper.lookupSubAccountTypePassport(user.getTenantId(), user.getName(), UserSourceEnum.getAllUserSource());
//    }

    @Override
    public int updateCustomRoleStatus(UpdateCustomRoleStatusVo updateCustomRoleStatusVo) {
        log.info("UpdateCustomRoleStatus parameter={}", JSON.toJSONString(updateCustomRoleStatusVo));
//        int deleteCount = userPropsMapper.delete(new UpdateWrapper<UserProps>()
//                .eq("user_id", updateCustomRoleStatusVo.getTenantId()).eq("prop_key", UserPropEnum.CUSTOM_ROLE_STATUS.getKey()));
        int deleteCount = userPropsMapper.delete(new UpdateWrapper<UserProps>()
                .eq("prop_key", UserPropEnum.CUSTOM_ROLE_STATUS.getKey()));
        log.info("UpdateCustomRoleStatus deleteCount={}", deleteCount);

        UserProps userProps = new UserProps();
        //userProps.setUserId(updateCustomRoleStatusVo.getTenantId());
        userProps.setPropKey(UserPropEnum.CUSTOM_ROLE_STATUS.getKey());
        userProps.setPropValue(updateCustomRoleStatusVo.getStatus() + "");
        int insertCount = userPropsMapper.insert(userProps);
        log.info("UpdateCustomRoleStatus insertCount={}", insertCount);
        return insertCount;
    }

//    @Override
//    public boolean getTenantCustomRoleFlag(Long tenantId) {
//        UserProps userProps = userPropsMapper.selectOne(new QueryWrapper<UserProps>().eq("user_id", tenantId).eq("prop_key", UserPropEnum.CUSTOM_ROLE_STATUS));
//        if (userProps == null) {
//            return false;
//        }
//        return Objects.equals(userProps.getPropValue(), "ENABLE");
//    }

    protected void saveUserProps(UserVo userVo, Long userId) {
        try {
            List<PropVo> props = userVo.getProps();
            PropVo e = new PropVo();
            e.setKey("secret_level");
            e.setValue("5");
            props.add(e);
            if (props.size() > 0) {
                props.forEach(propVo -> {
                    UserProps userProps = new UserProps(userId, propVo.getKey(), propVo.getValue());
                    userPropsMapper.insert(userProps);
                    log.info("Save Success SaveUserProps UserId={},PropKey={},PropValue={} ", userId, propVo.getKey(), propVo.getValue());
                });
            }
        } catch (DuplicateKeyException e) {
            log.error("SaveUserProps DuplicateKeyException UserId={}", userId);
        }
    }

    protected void updateUserProps(UserVo userVo, Long userId) {
        try {
            if (userVo.getProps().size() > 0) {
                Set<String> propKeys = userVo.getProps().stream()
                        .filter(p -> !Arrays.asList("secret_level").contains(p.getKey()))
                        .map(PropVo::getKey).collect(Collectors.toSet());
                int deleteCount = userPropsMapper.delete(new UpdateWrapper<UserProps>().eq("user_id", userId).in("prop_key", propKeys));
                log.info("UpdateUserProps deleteUserProps deleteCount={},userId={},propKeys={}", deleteCount, userId, propKeys);
                saveUserProps(userVo, userId);
            }
        } catch (DuplicateKeyException e) {
            log.error("UpdateUserProps DuplicateKeyException UserId={}", userId);
        }
    }

    protected void saveUserAKSK(User user, String user_ak, Map<String, String> userAKSK, String user_sk) {
        try {
            updateUserProps(user.getId(), user_ak, userAKSK.get(user_ak));
            updateUserProps(user.getId(), user_sk, userAKSK.get(user_sk));
            updateUserProps(user.getId(), "userAkSk-status", AkSkStatusEnum.NORMAL.name());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            updateUserProps(user.getId(), "userAk.create-time", sdf.format(new Date()));
        } catch (DuplicateKeyException e) {
            e.printStackTrace();
            log.error("SaveUserAKSK DuplicateKeyException UserId={}", user.getId());
        }
    }

//    /**
//     * 根据用户参数获取用户信息
//     */
//    private User getUserByUserVo(UserVo userVo, boolean syncFlag) {
//        User user;
//        if (syncFlag) { // name 不为空 说明手动同步
//            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
//            queryWrapper.eq("name", userVo.getName());
//            queryWrapper.eq("tenant_id", userVo.getTenant().getId());
//            queryWrapper.apply("tenant_id != id");
//            queryWrapper.eq("status", "NORMAL");
//            user = userMapper.selectOne(queryWrapper);
//        } else {// 请求触发
//            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
//            queryWrapper.eq("status", "NORMAL");
//            user = userMapper.selectOne(queryWrapper);
//            log.info("getUserByUserVo name is Empty parameter={}", JSON.toJSONString(userVo));
//        }
//        return user;
//    }

    @Override
    @Transactional
    public Map<String, Object> checkLogin(LoginVo parameter, String sessionId) {

        //1.1 获取当前用户租户相关全局属性
        AuthUser authUser = userMapper.lookupAuthUserByUserName(parameter.getUsername());
        if (authUser == null) {
            return PrivilegeUtils.checkResult("003", "登录用户名或登录密码不正确", null, "0");
        }
        List<KcdeTenantVo> tenants = userTenantMapper.getUserTenants(authUser.getId());
        if (tenants != null) {
            tenants.forEach(t -> authUser.attachTenant(new AuthUser.InnerTenant(t.getId(), t.getName(), t.getStatus())));
        }
//        boolean isTenant = authUser.getIsTenant();
        //1.2封装当前租户key value
        List<UserProps> userPropsList = userMapper.lookupUserPropsByUserId(authUser.getTenant().getId());
        Map<String, String> propsMap = userPropsList.stream().collect(Collectors.toMap(UserProps::getPropKey, UserProps::getPropValue));
        String pwdValid = propsMap.get(PropVoConstant.USER_PROP_KEY_PWD_VALID);
        String pwdValidDay = propsMap.get(PropVoConstant.USER_PROP_KEY_PWD_VALID_DAY);
        String pwdGenerateTime = propsMap.get(PropVoConstant.USER_PROP_KEY_PWD_GENERATE_TIME);
        String deadline = propsMap.get(PropVoConstant.USER_PROP_KEY_DEADLINE);
        String deadlineValidDay = propsMap.get(PropVoConstant.USER_PROP_KEY_DEADLINE_VALID_DAY);

//        //1.6 如果当前用户为子用户 则获取子用户业务属性值
//        if (!isTenant) {
        List<UserProps> subUserProps = userMapper.lookupUserPropsByUserId(authUser.getId());
        Map<String, String> subPropsMap = subUserProps.stream().collect(Collectors.toMap(UserProps::getPropKey, UserProps::getPropValue));
        deadline = subPropsMap.get(PropVoConstant.USER_PROP_KEY_DEADLINE);
        pwdGenerateTime = subPropsMap.get(PropVoConstant.USER_PROP_KEY_PWD_GENERATE_TIME);
        deadlineValidDay = subPropsMap.get(PropVoConstant.USER_PROP_KEY_DEADLINE_VALID_DAY);
//        }

        //更新登陆时间
        userMapper.updateUserProps(authUser.getId(), PropVoConstant.USER_PROP_KEY_LOGIN_TIME, LocalDateTime.now().format(DateTimeFormatter.ofPattern(Constants.DAY_FORMAT)));

        // 4.校验密码是否过期 获取用户密码时间 与当前时间比较是否大于3个月
        if (Objects.equals(pwdValid, UserStatusEnum.ENABLE.toString()) && DateUtil.afterDate(pwdGenerateTime, Integer.parseInt(pwdValidDay))) {
            return PrivilegeUtils.checkResult("004", "密码已过期 请修改", authUser, "0");
        }

        // 5.校验当前账号是否为休眠状态或者登陆状态
        if (Objects.equals(authUser.getStatus(), AuthUser.Status.SLEEP) || Objects.equals(authUser.getStatus(), AuthUser.Status.LOGOUT)) {
            return PrivilegeUtils.checkResult("009", "非正常状态无法登陆", authUser, "0");
        }

        // 9. 判断账号临时用户是否过期
        if (Objects.equals("TEMP", deadline)) {
            Date createTime = authUser.getCreateTime();
            SimpleDateFormat formatter = new SimpleDateFormat(Constants.DAY_FORMAT);
            String dateString = formatter.format(createTime);
            log.info("checkLogin tempUser currentTime={} ,deadlineValidDay={}", dateString, deadlineValidDay);
            if (DateUtil.afterDate(dateString, Integer.parseInt(deadlineValidDay))) {
                return PrivilegeUtils.checkResult("007", "临时用户已到期", authUser, "0");
            }
        }

//        // 10. 判断账号是否首次登陆
//        if (!authUser.getIsTenant() && authUser.isResetPwdWhenFirstLogin()) {
//            return PrivilegeUtils.checkResult("008", "首次用户 需重置密码登陆", authUser, "0");
//        }
        return PrivilegeUtils.checkResult("200", "ok", authUser, "200");
    }

    @Override
    public Optional<List<UserVo>> getUsersByRole(Long roleId) {
        List<User> list = userMapper.getUserByRole(roleId);
        if (list != null) {
            List<UserVo> userVoList = new ArrayList<>();
            for (User user : list) {
                UserVo userVo = convertToVo(user);
                userVoList.add(userVo);
            }
            return Optional.of(userVoList);
        }
        return Optional.empty();
    }
}
