package com.ksyun.auth.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.ksyun.auth.client.authentication.AuthUser;
import com.ksyun.auth.dao.*;
import com.ksyun.auth.dto.PrivilegePropsDto;
import com.ksyun.auth.dto.PrivilegeQueryDto;
import com.ksyun.auth.inter.KcdeClient;
import com.ksyun.auth.inter.response.LicenseResp;
import com.ksyun.auth.service.PrivilegeService;
import com.ksyun.auth.service.RoleService;
import com.ksyun.auth.utils.PrivilegeUtils;
import com.ksyun.auth.vo.*;
import com.ksyun.common.constant.Response;
import com.ksyun.common.entity.*;
import com.ksyun.common.enums.BusinessExceptionEnum;
import com.ksyun.common.enums.LicenseStatusEnum;
import com.ksyun.common.enums.PrivilegeCodeEnum;
import com.ksyun.common.enums.PrivilegeTypeEnum;
import com.ksyun.common.exception.Assert;
import com.ksyun.common.utils.CollectionUtil;
import com.ksyun.common.utils.TreeNode;
import jakarta.annotation.Resource;
import jodd.http.Cookie;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 权限点服务类
 * 主要权限点包含菜单和按钮
 */
@Service
@Slf4j
public class PrivilegeServiceImpl implements PrivilegeService {

    private static final String CONFIG_KEY_KSC_NETWORK = "network_tag";

    @Autowired
    private PrivilegeMapper privilegeMapper;
    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private PrivilegePropsMapper privilegePropsMapper;
    @Autowired
    private RolePrivilegeMapper rolePrivilegeMapper;
    @Autowired
    private GroupUserMapper groupUserMapper;
    @Autowired
    private GroupRoleMapper groupRoleMapper;
    @Autowired
    private RoleService roleService;
    @Autowired
    private PrivilegePermissionGroupMapper privilegePermissionGroupMapper;
    @Autowired
    private RolePermissionGroupMapper rolePermissionGroupMapper;
    @Autowired
    private BasicAppMapper basicAppMapper;

    @Autowired
    KcdeClient kcdeClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(PrivilegeUpdateVo privilegeUpdateVo, Integer level) {
        log.info("Request SavePrivilege Parameter = {}", privilegeUpdateVo);
        validateMenuLevel(privilegeUpdateVo,level);
        lookupExistNameAndCode(privilegeUpdateVo.getCode(), privilegeUpdateVo.getName());
        if (StringUtils.isNotEmpty(privilegeUpdateVo.getUrl())) {
            checkUrl(privilegeUpdateVo.getUrl(), privilegeUpdateVo.getType());
        }

        Privilege privilege = new Privilege();
        buildPrivilegeParameter(privilegeUpdateVo, privilege, false);
        privilegeMapper.insert(privilege);
        if (CollectionUtil.isNotEmpty(privilegeUpdateVo.getMenuProps())) {
            for (PrivilegePropsVo menuProp : privilegeUpdateVo.getMenuProps()) {
                PrivilegeProps privilegeProps = new PrivilegeProps();
                privilegeProps.setPrivilegeId(privilege.getId());
                privilegeProps.setPropKey(menuProp.getPropKey());
                privilegeProps.setPropValue(menuProp.getPropValue());
                int propsCount = privilegePropsMapper.insert(privilegeProps);
                log.info("Save PrivilegeProps Success Count = {} ,parameter={}", propsCount, JSON.toJSONString(privilegeProps));
            }
        }
        log.info("Save Privilege Success : code={}, name={}, id={}", privilegeUpdateVo.getCode(), privilegeUpdateVo.getName(), privilege.getId());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(PrivilegeUpdateVo privilegeUpdateVo, Integer level) {
        log.info("Request Update Privilege Parameter = {}", privilegeUpdateVo);

        validateMenuLevel(privilegeUpdateVo,level);
        checkMenuParentIdLegal(privilegeUpdateVo);
        LambdaQueryWrapper<Privilege> privilegeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        privilegeLambdaQueryWrapper.eq(Privilege::getId, privilegeUpdateVo.getPrivilegeId());
        Privilege privilege = privilegeMapper.selectById(privilegeUpdateVo.getPrivilegeId());

        Assert.notNull(privilege, BusinessExceptionEnum.PRIVILEGE_NOT_FOUND);
        if (!Objects.equals(privilege.getCode(), privilegeUpdateVo.getCode())) {
            QueryWrapper<Privilege> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("code", privilegeUpdateVo.getCode());
            Assert.isTrue(privilegeMapper.selectList(queryWrapper1).isEmpty(), BusinessExceptionEnum.PRIVILEGE_EXISTED);
        }
        if (!StringUtils.isEmpty(privilegeUpdateVo.getUrl())) {
            checkUrl(privilegeUpdateVo.getUrl(), privilegeUpdateVo.getType());
        }

        buildPrivilegeParameter(privilegeUpdateVo, privilege, true);
        PrivilegeProps privilegeProps = new PrivilegeProps();
        privilegeProps.setPrivilegeId(privilegeUpdateVo.getPrivilegeId());

        if (privilegeUpdateVo.getMenuProps() != null && !privilegeUpdateVo.getMenuProps().isEmpty()) {
            QueryWrapper<PrivilegeProps> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("privilege_id", privilegeProps.getPrivilegeId());
            int deletePropsCount = privilegePropsMapper.delete(queryWrapper);
            for (PrivilegePropsVo menuProp : privilegeUpdateVo.getMenuProps()) {
                PrivilegeProps privilegeProps1 = new PrivilegeProps();
                privilegeProps1.setPropKey(menuProp.getPropKey());
                privilegeProps1.setPropValue(menuProp.getPropValue());
                privilegeProps1.setPrivilegeId(privilegeProps.getPrivilegeId());
                int savePropsCount = privilegePropsMapper.insert(privilegeProps1);
                log.info("Save PrivilegeProps Success deleteCount={} ,savePropsCount={} ,parameter={}", deletePropsCount, savePropsCount, JSON.toJSONString(privilegeProps));
            }
        }

        privilegeMapper.updateById(privilege);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(PrivilegeDeleteVo parameter) {
        //判断传入权限集合是否合法
        QueryWrapper<Privilege> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(CollectionUtil.isNotEmpty(parameter.getPrivilegeIds()), "id", parameter.getPrivilegeIds());
        Long count = privilegeMapper.selectCount(queryWrapper);
        Assert.isTrue(parameter.getPrivilegeIds().size() == count, BusinessExceptionEnum.PARAM_INVALID);

        //判断删除集合中是否有父节点
        QueryWrapper<Privilege> privilegeQueryWrapper = new QueryWrapper<>();
        privilegeQueryWrapper.in(CollectionUtil.isNotEmpty(parameter.getPrivilegeIds()), "parent_id", parameter.getPrivilegeIds());
        Long parentCount = privilegeMapper.selectCount(privilegeQueryWrapper);
        Assert.isTrue(parentCount == 0, BusinessExceptionEnum.PRIVILEGE_HAS_CHILD_NODE);

        //解绑权限与角色关系
        QueryWrapper<RolePrivilege> rolePrivilegeQueryWrapper = new QueryWrapper<>();
        rolePrivilegeQueryWrapper.in(CollectionUtil.isNotEmpty(parameter.getPrivilegeIds()), "privilege_id", parameter.getPrivilegeIds());
        int unBindPrivilegeRoleCount = rolePrivilegeMapper.delete(rolePrivilegeQueryWrapper);
        //删除权限属性集合
        QueryWrapper<PrivilegeProps> privilegePropsQueryWrapper = new QueryWrapper<>();
        privilegePropsQueryWrapper.in(CollectionUtil.isNotEmpty(parameter.getPrivilegeIds()), "privilege_id", parameter.getPrivilegeIds());
        int deletePrivilegePropsCount = privilegePropsMapper.delete(privilegePropsQueryWrapper);
        //删除权限集合
        int deletePrivilegeCount = privilegeMapper.deleteBatchIds(parameter.getPrivilegeIds());
        log.info("Delete Privilege UnBindPrivilegeRoleCount={}, DeletePrivilegePropsCount={}, DeletePrivilegeCount={},Parameter={}",
                unBindPrivilegeRoleCount, deletePrivilegePropsCount, deletePrivilegeCount, parameter.getPrivilegeIds());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBindPrivilegeRole(RolePrivilegeVo parameters) {
        log.info("Request BindPrivilegeRole Parameter = {}", JSON.toJSONString(parameters));

        if (!parameters.getRoleIds().isEmpty()) {
            Long roleCount = roleService.getRolesCountByIds(parameters.getRoleIds());
            Assert.isTrue(roleCount == parameters.getRoleIds().size(), BusinessExceptionEnum.PARAM_INVALID);
        }
        QueryWrapper<RolePrivilege> rolePrivilegeQueryWrapper = new QueryWrapper<>();
        rolePrivilegeQueryWrapper.eq(parameters.getPrivilegeId() != null, "privilege_id", parameters.getPrivilegeId());
        int unBindPrivilegeRoleCount = rolePrivilegeMapper.delete(rolePrivilegeQueryWrapper);
        log.info("UnBindPrivilegeRole Count={}, Parameter={}", unBindPrivilegeRoleCount, parameters.getPrivilegeId());

        if (!parameters.getRoleIds().isEmpty()) {

            for (Long roleId : parameters.getRoleIds()) {
                RolePrivilege rolePrivilege = new RolePrivilege();
                rolePrivilege.setPrivilegeId(parameters.getPrivilegeId());
                rolePrivilege.setRoleId(roleId);
                rolePrivilege.setCreateTime(LocalDateTime.now());
                int bindPrivilegeRoleCount = rolePrivilegeMapper.insert(rolePrivilege);
                log.info("BindPrivilegeRole Count={}, Parameter={}", bindPrivilegeRoleCount, parameters.getPrivilegeId());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addGrantPrivilege(GrantPrivilegeVo grantPrivilegeVo) {
        log.info("grantPrivilege Parameter = {}", JSON.toJSONString(grantPrivilegeVo));
        Role role = roleMapper.selectById(grantPrivilegeVo.getRoleId());
        Assert.isTrue(role != null, BusinessExceptionEnum.ROLE_NOT_FOUND);

        if (null != grantPrivilegeVo.getPrivilegeIds() && !grantPrivilegeVo.getPrivilegeIds().isEmpty()) {
            PrivilegeQueryDto queryDto = new PrivilegeQueryDto();
            queryDto.setIds(grantPrivilegeVo.getPrivilegeIds());

            QueryWrapper<Privilege> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(queryDto.getParentId() != null, "parent_id", queryDto.getParentId());
            queryWrapper.eq(queryDto.getType() != null, "type", queryDto.getType());
            queryWrapper.like(queryDto.getName() != null, "name", queryDto.getName());
            queryWrapper.in(CollectionUtil.isNotEmpty(queryDto.getIds()), "id", queryDto.getIds());
            queryWrapper.eq(!StringUtil.isEmpty(queryDto.getCode()), "code", queryDto.getCode());
            queryWrapper.eq(!StringUtil.isEmpty(queryDto.getAppCode()), "app_code", queryDto.getAppCode());
            queryWrapper.orderByAsc("`order`");
            List<Privilege> items = privilegeMapper.selectList(queryWrapper);
            Assert.isTrue(items.size() == queryDto.getIds().size(), BusinessExceptionEnum.PARAM_INVALID);
        }

        QueryWrapper<RolePrivilege> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", grantPrivilegeVo.getRoleId());
        int count = rolePrivilegeMapper.delete(queryWrapper);
        log.info("unbindRolePrivilegeByRoleId roleId={}, count={}", grantPrivilegeVo.getRoleId(), count);

        if (!grantPrivilegeVo.getPrivilegeIds().isEmpty()) {
            for (Long privilegeId : grantPrivilegeVo.getPrivilegeIds()) {
                RolePrivilege rolePrivilege = new RolePrivilege();
                rolePrivilege.setRoleId(grantPrivilegeVo.getRoleId());
                rolePrivilege.setPrivilegeId(privilegeId);
                rolePrivilege.setCreateTime(LocalDateTime.now());
                int insertCount = rolePrivilegeMapper.insert(rolePrivilege);
                log.info("bindRolePrivilege roleId={},privilegeId={}, count={}", grantPrivilegeVo.getRoleId(), grantPrivilegeVo.getPrivilegeIds(), insertCount);
            }

        }
    }

    @Override
    public Set<Long> getRolePrivileges(Set<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return new HashSet<>();
        }
        QueryWrapper<RolePrivilege> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("role_id", roleIds);
        return rolePrivilegeMapper.selectList(queryWrapper).stream().map(RolePrivilege::getPrivilegeId).collect(Collectors.toSet());
    }



    @Override
    public PrivilegeVo get(int id) {
        Privilege privilege = privilegeMapper.selectById(id);
        Assert.notNull(privilege, BusinessExceptionEnum.PRIVILEGE_NOT_FOUND);
        PrivilegeVo privilegeVo = new PrivilegeVo();
        BeanUtils.copyProperties(privilege, privilegeVo);
        return privilegeVo;
    }

    @Override
    public List<PrivilegeVo> getAll() {
        QueryWrapper<Privilege> privilegeQueryWrapper = new QueryWrapper<>();
        privilegeQueryWrapper.orderByAsc("`order`");
        return privilegeMapper.selectList(privilegeQueryWrapper).stream().map(p -> {
            PrivilegeVo privilegeVo = new PrivilegeVo();
            BeanUtils.copyProperties(p, privilegeVo);
            return privilegeVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<PrivilegeVo> getAllByAK(String ak) {
        LambdaQueryWrapper<Privilege> privilegeQueryWrapper = new LambdaQueryWrapper<>();
        privilegeQueryWrapper.like(StringUtils.isNotEmpty(ak),Privilege::getAk,ak);
        privilegeQueryWrapper.orderByAsc(Privilege::getOrder);
        return privilegeMapper.selectList(privilegeQueryWrapper).stream().map(p -> {
            PrivilegeVo privilegeVo = new PrivilegeVo();
            BeanUtils.copyProperties(p, privilegeVo);
            return privilegeVo;
        }).collect(Collectors.toList());
    }

    /**
     * 运营平台左侧导航栏菜单查询，根据登录用户的权限查询
     */
    @Override
    public List<MenuPropsTreeItemVo> getIndexMenus(Cookie cookie, AuthUser user, HttpServletRequest request, String menuType) {
        return lookupMenus(user,menuType);
    }

//    /**
//     * 判断用户是否为租户
//     */
//    protected boolean isTenant(AuthUser authUser) {
//        boolean tenant = authUser.getTenant() != null && Objects.equals(authUser.getId(), authUser.getTenant().getId());
//        log.info("Request AuthUser isTenant = {} Parameter userId={}", tenant, authUser.getId());
//        return tenant;
//    }

    @Override
    public List<Privilege> getPrivilegeList(AuthUser user,String from) {
        PrivilegeQueryDto queryDto = new PrivilegeQueryDto();
        queryDto.setType(PrivilegeTypeEnum.MENU.getValue());
        List<Privilege> privilegeList;

        //获取用户角色
        Set<Long> userRoleIds = getUserRoleIds(user.getId());
        if (userRoleIds.isEmpty()) {
            return new ArrayList<>();
        }

        //根据用户查询权限组，根据权限组查询权限
        List<Long> privilegeIdsByRoleIds = getPrivilegeIdsByRoleIds(userRoleIds);

        LambdaQueryWrapper<Privilege> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Privilege::getId, privilegeIdsByRoleIds);
        queryWrapper.eq(null != queryDto.getType(), Privilege::getType, queryDto.getType());
        queryWrapper.like(!StringUtil.isEmpty(queryDto.getName()), Privilege::getName, queryDto.getName());
        queryWrapper.eq(!StringUtil.isEmpty(queryDto.getCode()), Privilege::getCode, queryDto.getCode());
        queryWrapper.notIn(null != queryDto.getNotIds() && !queryDto.getNotIds().isEmpty(), Privilege::getId, queryDto.getNotIds());
        queryWrapper.like(StringUtils.isNotEmpty(from),Privilege::getAk,from);
        queryWrapper.orderByAsc(Privilege::getOrder);
        privilegeList = privilegeMapper.selectList(queryWrapper);

//        //租户查询所有
//        if (isTenant(user)) {
//            LambdaQueryWrapper<Privilege> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(null != queryDto.getType(), Privilege::getType, queryDto.getType());
//            queryWrapper.like(!StringUtil.isEmpty(queryDto.getName()), Privilege::getName, queryDto.getName());
//            queryWrapper.eq(!StringUtil.isEmpty(queryDto.getCode()), Privilege::getCode, queryDto.getCode());
//            queryWrapper.notIn(null != queryDto.getNotIds() && !queryDto.getNotIds().isEmpty(), Privilege::getId, queryDto.getNotIds());
//            queryWrapper.like(StringUtils.isNotEmpty(from),Privilege::getAk,from);
//            queryWrapper.orderByAsc(Privilege::getOrder);
//            privilegeList = privilegeMapper.selectList(queryWrapper);
//        } else {
//            //获取子用户角色
//            Set<Long> userRoleIds = getUserRoleIds(user.getId());
//            if (userRoleIds.isEmpty()) {
//                return new ArrayList<>();
//            }
//            //根据子用户查询权限组，根据权限组查询权限
//            List<Long> privilegeIdsByRoleIds = getPrivilegeIdsByRoleIds(userRoleIds);
//            LambdaQueryWrapper<Privilege> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//            lambdaQueryWrapper.in(Privilege::getId, privilegeIdsByRoleIds);
//            lambdaQueryWrapper.eq(Privilege::getType, 1);
//            lambdaQueryWrapper.like(StringUtils.isNotEmpty(from),Privilege::getAk,from);
//            lambdaQueryWrapper.orderByAsc(Privilege::getOrder);
//            privilegeList = privilegeMapper.selectList(lambdaQueryWrapper);
//        }
        return privilegeList;
    }

    @Override
    public List<BasicAppVo> lookupAppList() {
        return basicAppMapper.selectList(null).stream().map(b -> new BasicAppVo(b.getAk(), b.getDescription())).collect(Collectors.toList());
    }

    private List<MenuPropsTreeItemVo> lookupMenus(AuthUser user,String menuType) {
        List<Privilege> privilegeList = getPrivilegeList(user,menuType);
        List<PrivilegeVo> privilegeVoList = privilegeList.stream().map(p -> {
            PrivilegeVo privilegeVo = new PrivilegeVo();
            BeanUtils.copyProperties(p, privilegeVo);
            privilegeVo.setParentId(p.getParentId() == null ? 0L : p.getParentId());
            return privilegeVo;
        }).collect(Collectors.toList());

        PrivilegeVo rp = new PrivilegeVo();
        rp.setId(0L);
        TreeNode<PrivilegeVo> root = new TreeNode<>(rp);
        //加上license过期判断
        boolean licenseValid = checkLicenseValid();
        if(!licenseValid){
            privilegeVoList = privilegeVoList.stream().filter(e -> (Objects.equals(e.getCode(),PrivilegeCodeEnum.KCDE_PTGL.getCode()) || Objects.equals(e.getCode(),PrivilegeCodeEnum.KCDE_PTGL_LICENSE.getCode()))).collect(Collectors.toList());
        }
        PrivilegeUtils.buildTree(privilegeVoList, root, 0L);
        return PrivilegeUtils.getMenusForFont(root, null);
    }

    @Override
    public List<String> getPrivilegeByUser(PrivilegeQueryVo parameter, AuthUser authUser, HttpServletRequest request) {
        //获取版本信息 判断当前版本是否有权限
        Set<Long> roleIds;
        List<String> privilegeCodes;
        PrivilegeQueryDto queryDto = new PrivilegeQueryDto();
        //queryDto.setType(2);

        roleIds = getUserRoleIds(authUser.getId());
        log.info("roleIds is {}", roleIds);
        List<Long> privilegeIdsByRoleIds = getPrivilegeIdsByRoleIds(roleIds);
        log.info("privilegeIdsByRoleIds is {}", privilegeIdsByRoleIds);
        if (privilegeIdsByRoleIds.isEmpty()){
            return Lists.newArrayList();
        }

        log.info("lookupCurrentUserIsPrivilege Request Parameter userId={} Am Tenant", authUser.getId());
        QueryWrapper<Privilege> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", privilegeIdsByRoleIds);
        queryWrapper.eq(null != queryDto.getType(), "type", queryDto.getType());
        queryWrapper.like(!StringUtil.isEmpty(queryDto.getName()), "name", queryDto.getName());
        queryWrapper.eq(!StringUtil.isEmpty(queryDto.getCode()), "code", queryDto.getCode());
        queryWrapper.notIn(CollectionUtil.isNotEmpty(queryDto.getNotIds()), "id", queryDto.getNotIds());
        queryWrapper.select("DISTINCT(id), code,`name`, type, icon, `order`, parent_id, url");
        queryWrapper.orderByAsc("`order`");
        List<Privilege> privilegeEntities = privilegeMapper.selectList(queryWrapper);
        privilegeCodes = privilegeEntities.stream().map(Privilege::getCode).collect(Collectors.toList());
//        if (isTenant(authUser)) {
//            log.info("lookupCurrentUserIsPrivilege Request Parameter userId={} Am Tenant", authUser.getId());
//            QueryWrapper<Privilege> queryWrapper = new QueryWrapper<>();
//            queryWrapper.in("id", privilegeIdsByRoleIds);
//            queryWrapper.eq(null != queryDto.getType(), "type", queryDto.getType());
//            queryWrapper.like(!StringUtil.isEmpty(queryDto.getName()), "name", queryDto.getName());
//            queryWrapper.eq(!StringUtil.isEmpty(queryDto.getCode()), "code", queryDto.getCode());
//            queryWrapper.notIn(CollectionUtil.isNotEmpty(queryDto.getNotIds()), "id", queryDto.getNotIds());
//            queryWrapper.select("DISTINCT(id), code,`name`, type, icon, `order`, parent_id, url");
//            queryWrapper.orderByAsc("`order`");
//            List<Privilege> privilegeEntities = privilegeMapper.selectList(queryWrapper);
//            privilegeCodes = privilegeEntities.stream().map(Privilege::getCode).collect(Collectors.toList());
//        } else {
//            //  子用户根据角色查询权限组 根据权限组查询权限code
//            roleIds = getUserRoleIds(authUser.getId());
//            log.info("roleIds is {}", roleIds);
//            List<Long> privilegeIdsByRoleIds = getPrivilegeIdsByRoleIds(roleIds);
//            log.info("privilegeIdsByRoleIds is {}", privilegeIdsByRoleIds);
//            if (privilegeIdsByRoleIds.isEmpty()){
//                return Lists.newArrayList();
//            }
//            LambdaQueryWrapper<Privilege> queryWrapperPrivilege = new LambdaQueryWrapper<>();
//            queryWrapperPrivilege.in(Privilege::getId, privilegeIdsByRoleIds);
//            List<Privilege> privileges = privilegeMapper.selectList(queryWrapperPrivilege);
//            privilegeCodes = privileges.stream().map(Privilege::getCode).collect(Collectors.toList());
//            log.info("lookupCurrentUserIsPrivilege Request Parameter userId={}, roleIds={}, mergedUserRoleIds={}", authUser.getId(), roleIds, roleIds);
//        }
        boolean licenseValid = checkLicenseValid();
        if(!licenseValid){
            privilegeCodes = privilegeCodes.stream().filter(e -> (Objects.equals(e, PrivilegeCodeEnum.KCDE_PTGL.getCode()) || Objects.equals(e,PrivilegeCodeEnum.KCDE_PTGL_LICENSE.getCode()) || Objects.equals(e,PrivilegeCodeEnum.KCDE_PTGL_LICENSE_IMPORT.getCode()))).collect(Collectors.toList());
        }
        return privilegeCodes;
    }

    private boolean checkLicenseValid(){
        Response.RichResponse<LicenseResp> respRichResponse = kcdeClient.getLicenseInfo();
        String licenseStatus = respRichResponse.getResult().getLicenseStatus();
        if(Objects.equals(LicenseStatusEnum.OUT_OF_DATE.getCode(),licenseStatus)) {
            log.error("license 状态：{}", LicenseStatusEnum.OUT_OF_DATE.getDescription());
            return false;
        } else if(Objects.equals(LicenseStatusEnum.UNAUTH.getCode(),licenseStatus)){
            log.error("license 状态：{}", LicenseStatusEnum.UNAUTH.getDescription());
            return false;
        }
        return true;
    }

    /**
     * @param roleIds
     * @return java.util.List<java.lang.Long>
     * @Description: 根据角色Id集合查询权限集合
     * <AUTHOR>
     * @Date 16:04 2021/4/6
     */
    private List<Long> getPrivilegeIdsByRoleIds(Collection<Long> roleIds) {
        //根据角色Id集合查询权限组Id集合
        if (roleIds.isEmpty()) {
            log.info("the user not have any roleId !");
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<RolePermissionGroup> rolePermissionGroupLambdaQueryWrapper = Wrappers.<RolePermissionGroup>lambdaQuery()
                .in(RolePermissionGroup::getRoleId, roleIds);
        List<RolePermissionGroup> rolePermissionGroups = rolePermissionGroupMapper.selectList(rolePermissionGroupLambdaQueryWrapper);
        if (Objects.isNull(rolePermissionGroups) || rolePermissionGroups.isEmpty()) {
            log.warn("no permission groups found for roles[{}]", roleIds.toString());
            return Lists.newArrayList();
        }
        Set<Long> permissionGroupIds = rolePermissionGroups.stream().map(RolePermissionGroup::getPermissionGroupId)
                .collect(Collectors.toSet());
        //根据权限id和权限组Id集合查询是否有权限
        LambdaQueryWrapper<PrivilegePermissionGroup> privilegePermissionGroupLambdaQueryWrapper = Wrappers.<PrivilegePermissionGroup>lambdaQuery()
                .in(PrivilegePermissionGroup::getPermissionGroupId, permissionGroupIds);
        List<PrivilegePermissionGroup> privilegePermissionGroups = privilegePermissionGroupMapper.selectList(privilegePermissionGroupLambdaQueryWrapper);
        if (Objects.isNull(privilegePermissionGroups) || privilegePermissionGroups.isEmpty()) {
            log.warn("Permissions are not assigned to the permission group[{}]", permissionGroupIds.toString());
            return new ArrayList<>();
        }
        return privilegePermissionGroups.stream().map(PrivilegePermissionGroup::getPrivilegeId)
                .collect(Collectors.toList());
    }

    @Override
    public List<PrivilegePropsDto> getPrivilegeProps(Set<Long> privilegeIds, List<String> propKeys) {
        QueryWrapper<PrivilegeProps> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(null != privilegeIds && !privilegeIds.isEmpty(), "privilege_id", privilegeIds);
        queryWrapper.in(null != propKeys && !propKeys.isEmpty(), "prop_key", propKeys);
        List<PrivilegeProps> privilegeProps = privilegePropsMapper.selectList(queryWrapper);

        List<PrivilegePropsDto> list = Lists.newArrayList();
        if (privilegeProps != null && !privilegeProps.isEmpty()) {
            Map<Long, List<PrivilegeProps>> collect = privilegeProps.stream().collect(Collectors.groupingBy(PrivilegeProps::getPrivilegeId));
            for (Map.Entry<Long, List<PrivilegeProps>> kv : collect.entrySet()) {
                PrivilegePropsDto privilegePropsBo = new PrivilegePropsDto();
                privilegePropsBo.setPrivilegeId(kv.getKey());
                privilegePropsBo.setProps(kv.getValue().stream().map(p -> new PrivilegePropsVo(p.getPropKey(), p.getPropValue())).collect(Collectors.toList()));
                list.add(privilegePropsBo);
            }
        }
        return list;
    }

    private void lookupExistNameAndCode(String code, String name) {
        Privilege privilegeEntity = new Privilege();
        privilegeEntity.setCode(code);
        QueryWrapper<Privilege> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code).or().eq("name", name);
        Assert.isTrue(privilegeMapper.selectCount(queryWrapper) == 0, BusinessExceptionEnum.PRIVILEGE_EXISTED);
    }

    @Override
    public List<PrivilegeVo> getPrivileges(PrivilegeQueryDto queryDto) {
        LambdaQueryWrapper<Privilege> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(null != queryDto.getParentId(), Privilege::getParentId, queryDto.getParentId());
        queryWrapper.eq(null != queryDto.getType(), Privilege::getType, queryDto.getType());
        queryWrapper.like(!StringUtil.isEmpty(queryDto.getName()), Privilege::getName, queryDto.getName());
        queryWrapper.eq(!StringUtil.isEmpty(queryDto.getCode()), Privilege::getCode, queryDto.getCode());
        queryWrapper.in(null != queryDto.getIds() && !queryDto.getIds().isEmpty(), Privilege::getId, queryDto.getIds());
        queryWrapper.like(StringUtils.isNotEmpty(queryDto.getAk()),Privilege::getAk,queryDto.getAk());
        queryWrapper.orderByAsc(Privilege::getOrder);
        return privilegeMapper.selectList(queryWrapper).stream().map(p -> {
            PrivilegeVo privilegeVo = new PrivilegeVo();
            BeanUtils.copyProperties(p, privilegeVo);
            return privilegeVo;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean moveUpPrivilege(PrivilegeVo privilege) {
        //1.查询当前节点的order
        Long currPrivilegeOrder = privilege.getOrder();
        log.info("currPrivilegeOrder={}", currPrivilegeOrder);

        //2.查询上一个更小节点的order
        PrivilegeVo prePrivilegeOrder = getSmallerPrivilege(privilege.getParentId(), privilege.getOrder());
        if (prePrivilegeOrder == null) {
            return true;
        }
        log.info("prePrivilegeOrder={}", JSON.toJSONString(prePrivilegeOrder));
        //3.更新当前节点的order
        int updateCurrOrder = updateOrder(privilege.getId(), prePrivilegeOrder.getOrder());
        log.info("updateCurrOrder={}", updateCurrOrder);

        //4.更新上个节点的order
        int updatePreOrder = updateOrder(prePrivilegeOrder.getId(), currPrivilegeOrder);
        log.info("updatePreOrder={}", updatePreOrder);

        Boolean result = (updateCurrOrder > 0) && (updatePreOrder > 0);
        Assert.isTrue(result, BusinessExceptionEnum.PRIVILEGE_ORDER_UP);
        return result;
    }


    @Override
    @Transactional
    public Boolean moveDownPrivilege(PrivilegeVo privilegeEntity) {
        //1.查询当前节点的order
        Long currPrivilegeOrder = privilegeEntity.getOrder();
        //2.查询下一个更大节点的order
        PrivilegeVo afterPrivilegeEntity = getLargerPrivilege(privilegeEntity.getParentId(), privilegeEntity.getOrder());
        if (afterPrivilegeEntity == null) {
            log.info("afterPrivilegeEntity is null");
            return true;
        }
        log.info("afterPrivilegeEntity={}", afterPrivilegeEntity);
        //3.更新当前节点的order
        int updateCurrOrder = updateOrder(privilegeEntity.getId(), afterPrivilegeEntity.getOrder());
        log.info("updateCurrOrder={}", updateCurrOrder);

        //4.更新下个节点的order
        int updateAfterOrder = updateOrder(afterPrivilegeEntity.getId(), currPrivilegeOrder);
        log.info("updateAfterOrder={}", updateAfterOrder);

        boolean result = (updateCurrOrder > 0) && (updateAfterOrder > 0);
        Assert.isTrue(result, BusinessExceptionEnum.PRIVILEGE_ORDER_DOWN);
        return result;
    }

    @Override
    public PrivilegeVo getSmallerPrivilege(Long parentId, Long order) {
        log.info("parentId = {},order ={}", parentId, order);
        Privilege privilege = privilegeMapper.selectSmallerPrivilege(parentId, order);
        Assert.notNull(privilege, BusinessExceptionEnum.PRIVILEGE_ORDER_UP);

        PrivilegeVo privilegeVo = new PrivilegeVo();
        BeanUtils.copyProperties(privilege, privilegeVo);
        return privilegeVo;
    }

    @Override
    public PrivilegeVo getLargerPrivilege(Long parentId, Long order) {
        log.info("parentId = {},order ={}", parentId, order);
        Privilege privilege = privilegeMapper.selectLargerPrivilege(parentId, order);
        Assert.notNull(privilege, BusinessExceptionEnum.PRIVILEGE_ORDER_DOWN);
        PrivilegeVo privilegeVo = new PrivilegeVo();
        BeanUtils.copyProperties(privilege, privilegeVo);
        return privilegeVo;
    }

    @Override
    @Transactional
    public int updateOrder(Long privilegeId, Long order) {
        Privilege privilege = new Privilege();
        privilege.setId(privilegeId);
        privilege.setOrder(order);
        return privilegeMapper.updateById(privilege);
    }

    @Override
    public int updatePrePrivilegeOrders(Long parentId, Long currOrder) {
        log.info("parentId = {},currOrder = {}", parentId, currOrder);
        return privilegeMapper.updatePrePrivilegeOrders(parentId, currOrder);
    }

    @Override
    public int updateAfterPrivilegeOrders(Long parentId, Long currOrder) {
        log.info("parentId = {},order = {}", parentId, currOrder);
        return privilegeMapper.updateAfterPrivilegeOrders(parentId, currOrder);
    }

    @Override
    @Transactional
    public boolean setTopPrivilege(PrivilegeVo privilegeEntity) {
        //1.查询同一级别order最小节点
        PrivilegeVo firstPrivilegeEntity = getFirstMinPrivilege(privilegeEntity);
        if (firstPrivilegeEntity.getId().equals(privilegeEntity.getId())) {
            log.info("firstPrivilegeEntity={}", JSON.toJSONString(firstPrivilegeEntity));
            return true;
        }

        Long currOrder = privilegeEntity.getOrder();

        //2.从第一个节点到当前节点的前一个节点，每个节点均加1
        int updatePreListResult = updatePrePrivilegeOrders(privilegeEntity.getParentId(), currOrder);
        log.info("updatePreListResult={}", updatePreListResult);

        //3.更新当前节点order为最小节点的order
        int updateCurrOrder = updateOrder(privilegeEntity.getId(), firstPrivilegeEntity.getOrder());

        boolean result = (updateCurrOrder > 0) && (updatePreListResult > 0);
        Assert.isTrue(result, BusinessExceptionEnum.PRIVILEGE_ORDER_TOP);
        return result;
    }

    @Override
    public boolean setButtomPrivilege(PrivilegeVo privilegeEntity) {
        log.info("privilegeEntity={}", JSON.toJSONString(privilegeEntity));
        //1.查询同一级别order最大节点
        PrivilegeVo lastMaxPrivilegeEntity = getLastMaxPrivilege(privilegeEntity);
        if (lastMaxPrivilegeEntity.getId().equals(privilegeEntity.getId())) {
            return true;
        }
        log.info("lastMaxPrivilegeEntity={}", JSON.toJSONString(lastMaxPrivilegeEntity));
        Long currOrder = privilegeEntity.getOrder();

        //2.从当前节点往下一个到 当前同一级别最下面节点，每个节点均减1
        int updateAfterListResult = updateAfterPrivilegeOrders(privilegeEntity.getParentId(), currOrder);
        log.info("updateAfterListResult={}", updateAfterListResult);

        //3.更新当前节点order为最大节点的order
        int updateCurrOrder = updateOrder(privilegeEntity.getId(), lastMaxPrivilegeEntity.getOrder());
        log.info("updateCurrOrder={}", updateCurrOrder);
        Boolean updateResult = (updateCurrOrder > 0) && (updateAfterListResult > 0);
        Assert.isTrue(updateResult, BusinessExceptionEnum.PRIVILEGE_ORDER_BUTTOM);
        return updateResult;
    }

    @Override
    public PrivilegeVo getFirstMinPrivilege(PrivilegeVo privilegeEntity) {
        log.info("privilegeEntity Parameter = {}", JSON.toJSONString(privilegeEntity));
        Privilege privilege = privilegeMapper.selectFirstMinPrivilege(privilegeEntity.getParentId());
        PrivilegeVo privilegeVo = new PrivilegeVo();
        BeanUtils.copyProperties(privilege, privilegeVo);
        return privilegeVo;
    }

    @Override
    public PrivilegeVo getLastMaxPrivilege(PrivilegeVo privilegeEntity) {
        log.info("privilegeEntity Parameter = {}", JSON.toJSONString(privilegeEntity));
        Privilege privilege = privilegeMapper.selectLastMaxPrivilege(privilegeEntity.getParentId());
        PrivilegeVo privilegeVo = new PrivilegeVo();
        BeanUtils.copyProperties(privilege, privilegeVo);
        return privilegeVo;
    }

    /**
     * 初始化权限点参数
     */
    private void buildPrivilegeParameter(PrivilegeUpdateVo privilegeUpdateVo, Privilege privilege, boolean isUpdate) {
        privilege.setCode(privilegeUpdateVo.getCode());
        privilege.setAk(privilegeUpdateVo.getAk());
        privilege.setName(privilegeUpdateVo.getName());
        privilege.setType(privilegeUpdateVo.getType());
        privilege.setIcon(privilegeUpdateVo.getIcon());
        privilege.setParentId(null == privilegeUpdateVo.getParentId() ? 0L : privilegeUpdateVo.getParentId());
        privilege.setUrl(privilegeUpdateVo.getUrl());
        privilege.setCreateTime(LocalDateTime.now());

        if (!isUpdate || (Objects.equals(privilegeUpdateVo.getParentId(), 0) && Objects.equals(privilegeUpdateVo.getType(), 2))) {
            QueryWrapper<Privilege> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("parent_id", privilegeUpdateVo.getParentId());
            queryWrapper.orderByDesc("`order`");
            queryWrapper.last("limit 1");
            List<Privilege> privilegeList = privilegeMapper.selectList(queryWrapper);
            Long lastOrder = null;
            if (privilegeList != null && !privilegeList.isEmpty()) {
                lastOrder = privilegeList.get(0).getOrder();
            }
            privilege.setOrder(lastOrder == null ? 1 : lastOrder + 1);
            log.info("Init Menu Order lastOrder ={}, Parameter={}", lastOrder, privilegeUpdateVo.getParentId());
        }
        log.info("isUpdate={}", isUpdate);
    }

    /**
     * 获取当前用户角色集合
     * 租户获取角色体系所有角色
     * 子用户 获取所在分组 以及 用户本身角色合集
     */
    public Set<Long> getUserRoleIds(Long userId) {
        Set<Long> roleIds = new HashSet<>();
        // 用户群组角色
        QueryWrapper<GroupUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        Set<Long> groupIds = groupUserMapper.selectList(queryWrapper).stream().map(GroupUser::getGroupId).collect(Collectors.toSet());
        Set<Long> groupRoleIds = new HashSet<>();
        if (CollectionUtil.isNotEmpty(groupIds)) {
            QueryWrapper<GroupRole> groupRoleQueryWrapper = new QueryWrapper<>();
            groupRoleQueryWrapper.in("group_id", groupIds);
            groupRoleIds = groupRoleMapper.selectList(groupRoleQueryWrapper).stream().map(GroupRole::getRoleId).collect(Collectors.toSet());
        }
        // 查询用户本身的角色
        QueryWrapper<UserRole> userRoleQueryWrapper = new QueryWrapper<>();
        userRoleQueryWrapper.eq("user_id", userId);
        Set<Long> userRoleIds = userRoleMapper.selectList(userRoleQueryWrapper).stream().map(UserRole::getRoleId).collect(Collectors.toSet());
        roleIds.addAll(groupRoleIds);
        roleIds.addAll(userRoleIds);
        log.info("Request Am SubUser Parameter={},Result = {}", userId, roleIds);
        return roleIds;
    }

    private void checkMenuParentIdLegal(PrivilegeUpdateVo privilegeUpdateVo) {
        if (Objects.equals(privilegeUpdateVo.getType(), 1)) {
            Assert.isTrue(!Objects.equals(privilegeUpdateVo.getParentId(), privilegeUpdateVo.getPrivilegeId()), BusinessExceptionEnum.PRIVILEGE_NOT_SELECT_SELF);

            //父级节点不能是子级
            TreeNode<PrivilegeVo> subTree = getSubMenuTree(privilegeUpdateVo.getPrivilegeId());
            if (null == subTree) {
                log.warn("sub tree is null.");
            } else {
                Assert.isTrue(!subTree.containsNode(item -> item.getId().equals(privilegeUpdateVo.getParentId())), BusinessExceptionEnum.PRIVILEGE_NOT_SELECT_CHILD);
            }
        }
    }

    private TreeNode<PrivilegeVo> getSubMenuTree(Long rootId) {
        PrivilegeQueryDto queryDto = new PrivilegeQueryDto();
        queryDto.setType(PrivilegeTypeEnum.MENU.getValue());
        List<PrivilegeVo> privilegeEntities = getPrivileges(queryDto);
        PrivilegeVo rp = new PrivilegeVo();
        rp.setId(0L);
        TreeNode<PrivilegeVo> root = new TreeNode<>(rp);
        PrivilegeUtils.buildTree(privilegeEntities, root, 0L);
        PrivilegeVo currentNode = new PrivilegeVo();
        currentNode.setId(rootId);
        return root.searchNode(currentNode);
    }

    private boolean judgeStrArrayContainsSameStr(String[] strings) {
        int len = strings.length;
        for (int i = 0; i < len; i++) {
            String temp = strings[i];
            for (int j = i + 1; j < len; j++) {
                String string = strings[j];
                if (string.equals(temp)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 校验级别
     */
    private void validateLevel(Long parentId,Integer level) {
        if (!Objects.equals(parentId, 0L)) {
            for (int i = 1; i < level; i++) {
                if (parentId == null || Objects.equals(parentId, 0L)) {
                    return;
                }
                parentId = privilegeMapper.selectById(parentId).getParentId();
            }
            Assert.isTrue(parentId == null || Objects.equals(parentId, 0L), BusinessExceptionEnum.PRIVILEGE_TOO_DEEP_LEVEL);
        }
    }

    private void validateMenuLevel(PrivilegeUpdateVo privilegeUpdateVo, Integer level) {
        if (Objects.equals(privilegeUpdateVo.getType(), PrivilegeTypeEnum.MENU.getValue())) {
            validateLevel(privilegeUpdateVo.getParentId(),level);
        }
    }

    private void checkUrl(String url, Integer type) {
        if (type == 1) {
            Assert.isTrue(url.matches("((#|/[0-9a-zA-Z_!~*'(){}.;?:@&=+$,%#/-]+)+/?)$"), BusinessExceptionEnum.PRIVILEGE_URL_INVALID);
        } else {
            Assert.isTrue(url.matches("((/?)|(/[0-9a-zA-Z_!~*'(){}.;?:@&=+$,%#-]+)+/?)$"), BusinessExceptionEnum.PRIVILEGE_URL_INVALID);
        }

        Assert.isTrue(!judgeStrArrayContainsSameStr(url.split(";")), BusinessExceptionEnum.PRIVILEGE_DUPLICATED_URL);
    }

    private String getHeaderKscNetwork(HttpServletRequest request,String networkTag) {
        if (request.getHeader(CONFIG_KEY_KSC_NETWORK) == null) {
            return Objects.equals(networkTag, "") ? null : networkTag;
        }
        return request.getHeader(CONFIG_KEY_KSC_NETWORK);
    }

}