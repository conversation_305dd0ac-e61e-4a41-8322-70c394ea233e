package com.ksyun.common.enums;

import java.util.HashSet;
import java.util.Set;

public enum UserSourceEnum {
    LOCAL,LDAP,PASSPORT,OIDC,CAM,CMGT,UASS,SELF_UASS,CAS,HH;

    public static Set<String> getAllUserSource(){
        Set<String> userSourceList = new HashSet<>();
        userSourceList.add(LOCAL.name());
        userSourceList.add(LDAP.name());
        userSourceList.add(PASSPORT.name());
        userSourceList.add(OIDC.name());
        userSourceList.add(CAM.name());
        userSourceList.add(CMGT.name());
        userSourceList.add(UASS.name());
        userSourceList.add(SELF_UASS.name());
        // HH表示瀚海平台作为用户来源
        userSourceList.add(HH.name());
        return userSourceList;
    }

}
