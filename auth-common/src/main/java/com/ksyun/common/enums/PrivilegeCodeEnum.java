package com.ksyun.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

/**
 * 权限点code
 * <AUTHOR>
 * @date 2022-12-28
 */
@Getter
@AllArgsConstructor
public enum PrivilegeCodeEnum{

    KCDE_PTGL("KCDE_PTGL", "平台管理"),
    KCDE_PTGL_LICENSE("KCDE_PTGL_License","系统授权"),
    KCDE_PTGL_LICENSE_IMPORT("KCDE_PTGL_License_Import","系统授权-导入")

    ;

    /**
     * 返回码
     */
    private String code;
    /**
     * 返回消息
     */
    private String message;
}
