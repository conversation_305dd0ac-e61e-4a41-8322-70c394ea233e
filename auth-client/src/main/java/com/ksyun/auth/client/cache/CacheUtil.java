package com.ksyun.auth.client.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CacheUtil {

    private static final GenericCache<String, Object> GENERIC_CACHE = new GenericCache<>();


    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     * @return
     */
    public static boolean expire(String key, long time) {
        long startTime = logStart(key, time);
        try {
            if (time > 0) {
                Object value = GENERIC_CACHE.get(key);
                GENERIC_CACHE.put(key, value,time);
            }
            return true;
        } catch (Exception e) {
            log.error("redis option error!", e);
            return false;
        } finally {
            logEnd(startTime);
        }
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    /*public static long getExpire(String key) {
        long startTime = logStart(key);
        try {
            return redisUtil.redisTemplate.getExpire(key, TimeUnit.SECONDS);
        } finally {
            logEnd(startTime);
        }
    }*/

    /**
     * 获取指定前缀的key
     *
     * @param prefix key前缀
     */
//    public static Set<String> getPrefixKeyValue(String prefix) {
//        // 获取所有的key
//        return redisUtil.redisTemplate.keys(prefix.concat("*"));
//    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
//    public static boolean hasKey(String key) {
//        long startTime = logStart(key);
//        try {
//            return redisUtil.redisTemplate.hasKey(key);
//        } catch (Exception e) {
//            log.error("redis option error!", e);
//            return false;
//        } finally {
//            logEnd(startTime);
//        }
//    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    @SuppressWarnings("unchecked")
    public static void del(String... key) {
        long startTime = logStart(key);

        if (key != null && key.length > 0) {
            if (key.length == 1) {
                GENERIC_CACHE.remove(key[0]);
            } else {
                GENERIC_CACHE.removeAll(Arrays.asList(key));
                ;
            }
        }

        logEnd(startTime);
    }

    //============================String=============================

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    public static Object get(String key) {
        long startTime = logStart(key);
        try {
            return key == null ? null : GENERIC_CACHE.get(key);
        } finally {
            logEnd(startTime);
        }
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public static boolean set(String key, Object value) {
        long startTime = logStart(key, value);
        try {
            GENERIC_CACHE.put(key, value);
            return true;
        } catch (Exception e) {
            log.error("redis option error!", e);
            return false;
        } finally {
            logEnd(startTime);
        }

    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public static boolean set(String key, String value, long time) {
        long startTime = logStart(key, value, time);
        try {
            if (time > 0) {
                GENERIC_CACHE.put(key, value, time);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            log.error("redis option error!", e);
            return false;
        } finally {
            logEnd(startTime);
        }
    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public static boolean set(String key, Object value, long time) {
        long startTime = logStart(key, value, time);
        try {
            if (time > 0) {
                GENERIC_CACHE.put(key, value, time);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            log.error("redis option error!", e);
            return false;
        } finally {
            logEnd(startTime);
        }
    }

    private static long logStart(Object... args) {
        if (log.isDebugEnabled()) {
            log.debug("ThreadName: {}, Method invoke stack: [{}], Args: [{}]", getCurrentThreadName(), getMethodStacks(3), getArgs(args));
        }

        return System.currentTimeMillis();
    }

    private static void logEnd(long startTime) {
        if (log.isDebugEnabled()) {
            long endTime = System.currentTimeMillis();
            log.debug("ThreadName: {}, Method invoke stack: [{}], Used time: {}ms", getCurrentThreadName(),
                    getMethodStacks(3), endTime - startTime);
        }
    }

    private static String getCurrentThreadName() {
        return Thread.currentThread().getName();
    }

    private static String getMethodStacks(int depth) {
        StackTraceElement elements[] = Thread.currentThread().getStackTrace();

        int skip = 3;
        int newDepth = Math.min(depth, elements.length - skip);
        return Arrays.stream(elements).skip(skip).limit(newDepth).map(e -> e.getClass() + "->" + e.getMethodName())
                .collect(Collectors.joining(", "));
    }

    private static String getArgs(Object[] args) {
        if (args == null || args.length == 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < args.length; i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(args[i].toString());
        }

        return sb.toString();
    }



}
